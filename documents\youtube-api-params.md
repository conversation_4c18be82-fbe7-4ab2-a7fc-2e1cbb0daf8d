# Параметры взаимодействия с YouTube Data API v3

Этот документ описывает основные параметры запросов к YouTube Data API v3, которые используются для поиска видео, получения информации о видео, каналах и плейлистах.

## Общие параметры для всех запросов

Следующие параметры являются общими для большинства запросов YouTube Data API:

| Параметр | Описание | Пример |
|----------|----------|--------|
| `key` | Ваш API-ключ | `key=YOUR_API_KEY` |
| `part` | Части данных для возврата | `part=snippet,statistics` |
| `maxResults` | Количество результатов на странице (до 50) | `maxResults=10` |
| `pageToken` | Токен для получения следующей/предыдущей страницы | `pageToken=NEXT_PAGE_TOKEN` |

## 1. Поиск видео

Метод: `search.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/search`

### Основные параметры поиска

| Параметр | Описание | Пример |
|----------|----------|--------|
| `q` | Поисковый запрос | `q=artificial+intelligence` |
| `type` | Тип искомых элементов | `type=video` (также поддерживаются `channel`, `playlist`) |
| `order` | Порядок сортировки | `order=date` (доступны: `date`, `rating`, `relevance`, `title`, `videoCount`, `viewCount`) |
| `publishedAfter` | Поиск после указанной даты/времени | `publishedAfter=2023-01-01T00:00:00Z` |
| `publishedBefore` | Поиск до указанной даты/времени | `publishedBefore=2023-12-31T23:59:59Z` |
| `relevanceLanguage` | Приоритет результатам на указанном языке | `relevanceLanguage=ru` |
| `regionCode` | Приоритет результатам для указанного региона | `regionCode=RU` |
| `safeSearch` | Фильтрация результатов по признаку безопасности | `safeSearch=strict` (доступны: `moderate`, `none`, `strict`) |

### Дополнительные параметры для поиска видео

| Параметр | Описание | Пример |
|----------|----------|--------|
| `videoDuration` | Длительность видео | `videoDuration=medium` (доступны: `any`, `long` (>20 минут), `medium` (4-20 минут), `short` (<4 минут)) |
| `videoEmbeddable` | Только встраиваемые видео | `videoEmbeddable=true` |
| `videoLicense` | Фильтр по типу лицензии | `videoLicense=creativeCommon` (также доступен `youtube`) |
| `videoDefinition` | Разрешение видео | `videoDefinition=high` (также доступен `standard`) |
| `videoCaption` | Наличие субтитров | `videoCaption=closedCaption` (также доступны `any`, `none`) |
| `videoCategoryId` | ID категории видео | `videoCategoryId=10` (10 = Music) |
| `channelId` | Поиск по каналу | `channelId=CHANNEL_ID` |
| `channelType` | Тип канала | `channelType=any` (также доступен `show`) |
| `eventType` | Тип события | `eventType=live` (также доступны `completed`, `upcoming`) |

### Пример запроса поиска видео

```
GET https://www.googleapis.com/youtube/v3/search?part=snippet&q=machine+learning&type=video&order=viewCount&videoDuration=medium&publishedAfter=2023-01-01T00:00:00Z&maxResults=50&key=YOUR_API_KEY
```

## 2. Получение информации о видео

Метод: `videos.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/videos`

### Основные параметры запроса информации о видео

| Параметр | Описание | Пример |
|----------|----------|--------|
| `id` | ID видео или список через запятую | `id=VIDEO_ID` или `id=VIDEO_ID1,VIDEO_ID2` |
| `part` | Части информации для возврата | `part=snippet,statistics,contentDetails` |

### Доступные части (part) для информации о видео

| Значение part | Описание | Квота |
|---------------|----------|-------|
| `snippet` | Основная информация (заголовок, описание, миниатюры) | 2 |
| `contentDetails` | Технические детали (продолжительность, разрешение) | 2 |
| `statistics` | Статистика (просмотры, лайки, дизлайки, комментарии) | 2 |
| `status` | Статус (приватность, ограничения эмбеда) | 2 |
| `player` | HTML для встраивания видео | 0 |
| `topicDetails` | Темы, связанные с видео | 2 |
| `recordingDetails` | Информация о записи (локация, дата) | 2 |
| `fileDetails` | Информация о файле (разрешение, аудио/видео потоки) | 1 |
| `processingDetails` | Статус обработки видео | 1 |
| `suggestions` | Рекомендации по улучшению видео | 1 |
| `liveStreamingDetails` | Информация о трансляции (актуально для прямых эфиров) | 2 |
| `localizations` | Локализованные версии | 2 |

### Пример запроса информации о видео

```
GET https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics,contentDetails&id=VIDEO_ID&key=YOUR_API_KEY
```

## 3. Получение информации о канале

Метод: `channels.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/channels`

### Параметры идентификации канала (требуется хотя бы один)

| Параметр | Описание | Пример |
|----------|----------|--------|
| `id` | ID канала или список через запятую | `id=CHANNEL_ID` или `id=CHANNEL_ID1,CHANNEL_ID2` |
| `forUsername` | Имя пользователя канала (устаревший формат) | `forUsername=GoogleDevelopers` |
| `forHandle` | Пользовательское имя канала (новый формат) | `forHandle=UniverseofAIz` или `forHandle=@UniverseofAIz` |
| `mine` | Получение информации о своем канале (требует OAuth) | `mine=true` |

### Доступные части (part) для информации о канале

| Значение part | Описание | Квота |
|---------------|----------|-------|
| `snippet` | Основная информация (название, описание, миниатюры) | 2 |
| `contentDetails` | Плейлисты канала (uploads, likes, favorites) | 2 |
| `statistics` | Статистика (просмотры, подписчики, видео) | 2 |
| `status` | Статус (приватность) | 2 |
| `brandingSettings` | Настройки брендинга (баннеры, цвета) | 2 |
| `topicDetails` | Темы, связанные с каналом | 2 |
| `localizations` | Локализованные версии | 2 |

### Пример запроса информации о канале

```
GET https://www.googleapis.com/youtube/v3/channels?part=snippet,statistics,contentDetails&id=CHANNEL_ID&key=YOUR_API_KEY
```

## 4. Получение плейлистов канала

Метод: `playlists.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/playlists`

### Основные параметры запроса плейлистов

| Параметр | Описание | Пример |
|----------|----------|--------|
| `channelId` | ID канала для получения плейлистов | `channelId=CHANNEL_ID` |
| `id` | ID конкретного плейлиста или список через запятую | `id=PLAYLIST_ID` или `id=PLAYLIST_ID1,PLAYLIST_ID2` |
| `mine` | Получение своих плейлистов (требует OAuth) | `mine=true` |
| `part` | Части информации для возврата | `part=snippet,contentDetails` |

### Доступные части (part) для информации о плейлистах

| Значение part | Описание | Квота |
|---------------|----------|-------|
| `snippet` | Основная информация (название, описание, миниатюры) | 2 |
| `contentDetails` | Количество видео в плейлисте | 2 |
| `status` | Статус приватности | 2 |
| `player` | HTML для встраивания плейлиста | 0 |
| `localizations` | Локализованные версии | 2 |

### Пример запроса плейлистов канала

```
GET https://www.googleapis.com/youtube/v3/playlists?part=snippet,contentDetails&channelId=CHANNEL_ID&maxResults=25&key=YOUR_API_KEY
```

## 5. Получение видео из плейлиста

Метод: `playlistItems.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/playlistItems`

### Основные параметры запроса элементов плейлиста

| Параметр | Описание | Пример |
|----------|----------|--------|
| `playlistId` | ID плейлиста | `playlistId=PLAYLIST_ID` |
| `id` | ID конкретного элемента плейлиста | `id=PLAYLIST_ITEM_ID` |
| `part` | Части информации для возврата | `part=snippet,contentDetails` |
| `maxResults` | Количество результатов (до 50) | `maxResults=50` |
| `pageToken` | Токен для пагинации | `pageToken=NEXT_PAGE_TOKEN` |

### Доступные части (part) для элементов плейлиста

| Значение part | Описание | Квота |
|---------------|----------|-------|
| `snippet` | Основная информация о видео в плейлисте | 2 |
| `contentDetails` | ID видео и дата публикации | 2 |
| `status` | Статус (приватность элемента) | 2 |

### Пример запроса элементов плейлиста

```
GET https://www.googleapis.com/youtube/v3/playlistItems?part=snippet,contentDetails&playlistId=PLAYLIST_ID&maxResults=50&key=YOUR_API_KEY
```

## 6. Получение комментариев к видео

Метод: `commentThreads.list`
Базовый URL: `https://www.googleapis.com/youtube/v3/commentThreads`

### Основные параметры запроса комментариев

| Параметр | Описание | Пример |
|----------|----------|--------|
| `videoId` | ID видео для получения комментариев | `videoId=VIDEO_ID` |
| `channelId` | ID канала для получения комментариев | `channelId=CHANNEL_ID` |
| `part` | Части информации для возврата | `part=snippet,replies` |
| `order` | Порядок сортировки | `order=relevance` (доступны `relevance`, `time`) |
| `maxResults` | Количество результатов (до 100) | `maxResults=100` |
| `textFormat` | Формат текста | `textFormat=plainText` (или `html`) |
| `searchTerms` | Поиск по комментариям | `searchTerms=query` |

### Пример запроса комментариев видео

```
GET https://www.googleapis.com/youtube/v3/commentThreads?part=snippet,replies&videoId=VIDEO_ID&maxResults=100&order=relevance&key=YOUR_API_KEY
```

## 7. Полезные техники работы с API

### Получение всех видео канала

1. Получить ID плейлиста загрузок канала:
```
GET https://www.googleapis.com/youtube/v3/channels?part=contentDetails&id=CHANNEL_ID&key=YOUR_API_KEY
```

2. Альтернативно, ID плейлиста загрузок можно получить, заменив "UC" на "UU" в ID канала:
   - ID канала: `UC_x5XG1OV2P6uZZ5FSM9Ttw`
   - ID плейлиста загрузок: `UU_x5XG1OV2P6uZZ5FSM9Ttw`

3. Получить видео из плейлиста загрузок:
```
GET https://www.googleapis.com/youtube/v3/playlistItems?part=snippet,contentDetails&playlistId=UU_x5XG1OV2P6uZZ5FSM9Ttw&maxResults=50&key=YOUR_API_KEY
```

### Пагинация для получения большого количества данных

Для получения более 50 результатов используйте токен следующей страницы:

1. Сделать первый запрос:
```
GET https://www.googleapis.com/youtube/v3/search?part=snippet&q=query&maxResults=50&key=YOUR_API_KEY
```

2. Получить `nextPageToken` из ответа.

3. Использовать `nextPageToken` для следующего запроса:
```
GET https://www.googleapis.com/youtube/v3/search?part=snippet&q=query&maxResults=50&pageToken=NEXT_PAGE_TOKEN&key=YOUR_API_KEY
```

4. Повторять шаги 2-3, пока не получите все результаты или пока не будет отсутствовать `nextPageToken`.

## 8. Квоты API и лимиты

YouTube Data API имеет систему квот для ограничения использования ресурсов. По умолчанию каждый проект имеет квоту в 10,000 единиц в день. Различные методы и параметры потребляют разное количество единиц квоты.

### Основной расход квоты для популярных операций

| Операция | Расход квоты (единиц) |
|----------|----------------------|
| `search.list` | 100 |
| `videos.list` с part=snippet | 1 |
| `channels.list` с part=snippet | 1 |
| `playlists.list` с part=snippet | 1 |
| `playlistItems.list` с part=snippet | 1 |
| `commentThreads.list` с part=snippet | 1 |
| `videos.insert` (загрузка видео) | 1600 |

Каждый дополнительный part обычно добавляет 1-2 единицы квоты к запросу.

## 9. Обработка ошибок

При работе с YouTube API вы можете столкнуться со следующими кодами ошибок:

| Код ошибки | Описание | Возможное решение |
|------------|----------|-------------------|
| 400 | Bad Request | Проверьте параметры запроса |
| 401 | Unauthorized | Проверьте API ключ или токен OAuth |
| 403 | Forbidden | Превышение квоты или ограничения доступа |
| 404 | Not Found | Ресурс не существует (неверный ID) |
| 429 | Too Many Requests | Превышен лимит запросов, нужно подождать |
| 500, 503 | Server Error | Временные проблемы с API, повторите запрос позже |

### Пример ответа с ошибкой

```json
{
  "error": {
    "code": 403,
    "message": "The request cannot be completed because you have exceeded your quota.",
    "errors": [
      {
        "message": "The request cannot be completed because you have exceeded your quota.",
        "domain": "youtube.quota",
        "reason": "quotaExceeded"
      }
    ]
  }
}
```

## 10. Заключение

YouTube Data API v3 предоставляет широкие возможности для работы с видео, каналами, плейлистами и комментариями. Для наиболее эффективного использования API:

1. Оптимизируйте запросы, запрашивая только необходимые части данных (параметр `part`)
2. Используйте `search.list` только когда необходимо, т.к. он потребляет много квоты
3. Учитывайте лимиты пагинации и квоты при проектировании приложения
4. Для большинства операций чтения достаточно API-ключа, для действий от имени пользователя используйте OAuth 2.0

Для получения дополнительной информации обратитесь к [официальной документации YouTube Data API](https://developers.google.com/youtube/v3/docs/).