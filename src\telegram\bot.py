"""
Telegram-бот для YouTube Downloader

Этот модуль реализует Telegram-бота для взаимодействия с функционалом YouTube Downloader:
- Поиск видео по ключевым словам
- Загрузка аудио из видео
- Транскрибация аудио
- Получение списка видео из плейлистов и каналов

Автор: AI Team
"""

import os
import sys
import logging
import re
import json
import subprocess
from dotenv import load_dotenv
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, ContextTypes, filters

# Вспомогательные функции для работы с длительностью видео
def parse_duration(duration_iso):
    """
    Преобразует строку длительности в формате ISO 8601 (PT1H2M3S) в секунды.

    Args:
        duration_iso (str): Строка с длительностью в формате ISO 8601.

    Returns:
        int: Количество секунд.
    """
    if not duration_iso or not isinstance(duration_iso, str) or not duration_iso.startswith('PT'):
        return 0

    # Извлекаем часы, минуты, секунды с помощью регулярных выражений
    hours = re.search(r'(\d+)H', duration_iso)
    minutes = re.search(r'(\d+)M', duration_iso)
    seconds = re.search(r'(\d+)S', duration_iso)

    total_seconds = 0

    if hours:
        total_seconds += int(hours.group(1)) * 3600
    if minutes:
        total_seconds += int(minutes.group(1)) * 60
    if seconds:
        total_seconds += int(seconds.group(1))

    return total_seconds

def format_duration(duration_iso):
    """
    Преобразует длительность в формате ISO 8601 (PT1H2M3S) в читаемый формат (1:02:03).

    Args:
        duration_iso (str): Строка с длительностью в формате ISO 8601.

    Returns:
        str: Отформатированная строка времени.
    """
    if not duration_iso or not isinstance(duration_iso, str):
        return "0:00"

    total_seconds = parse_duration(duration_iso)

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60

    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

# Добавляем корневую директорию проекта в путь для импорта
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, PROJECT_ROOT)

# Импортируем необходимые модули проекта
try:
    from src.scrapers.youtube_api.youtube_api import search_videos, get_channel_videos, get_video_details, YouTubeApiClient
    from src.scrapers.apify.youtube_apify import YouTubeScraper
    from src.downloader.video_downloader import VideoDownloader
    from src.transcriber.Gemini.gemini_transcriber import transcribe_audio

    # Создаем экземпляр YouTube API клиента
    youtube_api_client = YouTubeApiClient()

    # Создаем экземпляр скрапера для работы с плейлистами
    youtube_scraper = YouTubeScraper()

    # Функция-обертка для получения видео из плейлиста
    def get_playlist_videos(playlist_id, max_results=5):
        playlist_url = f"https://www.youtube.com/playlist?list={playlist_id}"
        return youtube_scraper.get_playlist_videos(playlist_url, max_results)

    YOUTUBE_API_AVAILABLE = True
except ImportError as e:
    print(f"Ошибка импорта модулей: {e}")
    YOUTUBE_API_AVAILABLE = False

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Загружаем переменные окружения
load_dotenv()

# Получаем токен бота из переменных окружения
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
if not TELEGRAM_BOT_TOKEN:
    raise ValueError("TELEGRAM_BOT_TOKEN не найден в переменных окружения")

# Регулярные выражения для определения типа ввода
YOUTUBE_VIDEO_REGEX = r'(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:watch\?v=|embed\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})'
YOUTUBE_PLAYLIST_REGEX = r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/playlist\?list=([a-zA-Z0-9_-]+)'
YOUTUBE_CHANNEL_REGEX = r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/(?:channel\/|@)([a-zA-Z0-9_-]+)'

# Инициализируем загрузчик видео с YouTube API клиентом
downloader = VideoDownloader(api_client=youtube_api_client)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик команды /start"""
    await update.message.reply_text(
        "👋 Привет! Я бот для работы с YouTube.\n\n"
        "Что я умею:\n"
        "🔍 Искать видео по ключевым словам\n"
        "⬇️ Скачивать аудио из видео\n"
        "🎤 Транскрибировать аудио в текст и сохранять в файлы\n"
        "📋 Получать списки видео из плейлистов и каналов\n\n"
        "Отправь мне:\n"
        "- Ключевые слова для поиска\n"
        "- Ссылку на YouTube-видео\n"
        "- Ссылку на YouTube-плейлист\n"
        "- Ссылку на YouTube-канал\n\n"
        "Используй /help для получения дополнительной информации."
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик команды /help"""
    await update.message.reply_text(
        "📖 Инструкция по использованию бота:\n\n"
        "1️⃣ Поиск видео:\n"
        "   Просто отправь ключевые слова, например: 'Python tutorial'\n\n"
        "2️⃣ Скачивание аудио:\n"
        "   Отправь ссылку на YouTube-видео или его ID\n\n"
        "3️⃣ Получение списка видео из плейлиста:\n"
        "   Отправь ссылку на YouTube-плейлист\n\n"
        "4️⃣ Получение списка видео с канала:\n"
        "   Отправь ссылку на YouTube-канал\n\n"
        "5️⃣ Транскрибация:\n"
        "   После скачивания аудио нажми кнопку 'Транскрибировать'\n"
        "   Бот создаст транскрипцию и отправит ее в виде:\n"
        "   - Краткого превью в сообщении\n"
        "   - Полного текста в TXT файле\n"
        "   - Форматированного текста в Markdown файле\n\n"
        "Команды:\n"
        "/start - Начать работу с ботом\n"
        "/help - Показать эту справку\n"
        "/search [запрос] - Поиск видео по запросу\n"
    )

async def search_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик команды /search"""
    if not context.args:
        await update.message.reply_text("Пожалуйста, укажите поисковый запрос после команды /search")
        return

    query = ' '.join(context.args)
    await update.message.reply_text(f"🔍 Ищу видео по запросу: '{query}'...")

    try:
        results = search_videos(query, max_results=5)
        if not results:
            await update.message.reply_text("По вашему запросу ничего не найдено.")
            return

        for video in results:
            keyboard = [
                [
                    InlineKeyboardButton("⬇️ Скачать", callback_data=f"download_{video['id']}"),
                    InlineKeyboardButton("ℹ️ Подробнее", callback_data=f"details_{video['id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"🎬 {video['title']}\n"
                f"👤 {video['channelTitle']}\n"
                f"🔗 https://youtu.be/{video['id']}",
                reply_markup=reply_markup
            )
    except Exception as e:
        logger.error(f"Ошибка при поиске видео: {e}")
        await update.message.reply_text(f"Произошла ошибка при поиске: {str(e)}")

async def handle_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик входящих сообщений"""
    text = update.message.text

    # Проверяем, является ли сообщение ссылкой на YouTube-видео
    video_match = re.search(YOUTUBE_VIDEO_REGEX, text)
    if video_match:
        video_id = video_match.group(1)
        await process_video_link(update, context, video_id)
        return

    # Проверяем, является ли сообщение ссылкой на YouTube-плейлист
    playlist_match = re.search(YOUTUBE_PLAYLIST_REGEX, text)
    if playlist_match:
        playlist_id = playlist_match.group(1)
        await process_playlist_link(update, context, playlist_id)
        return

    # Проверяем, является ли сообщение ссылкой на YouTube-канал
    channel_match = re.search(YOUTUBE_CHANNEL_REGEX, text)
    if channel_match:
        channel_id = channel_match.group(1)
        await process_channel_link(update, context, channel_id)
        return

    # Если это просто ID видео (11 символов)
    if re.match(r'^[a-zA-Z0-9_-]{11}$', text):
        await process_video_link(update, context, text)
        return

    # В остальных случаях считаем, что это поисковый запрос
    await update.message.reply_text(f"🔍 Ищу видео по запросу: '{text}'...")
    try:
        results = search_videos(text, max_results=5)
        if not results:
            await update.message.reply_text("По вашему запросу ничего не найдено.")
            return

        for video in results:
            keyboard = [
                [
                    InlineKeyboardButton("⬇️ Скачать", callback_data=f"download_{video['id']}"),
                    InlineKeyboardButton("ℹ️ Подробнее", callback_data=f"details_{video['id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"🎬 {video['title']}\n"
                f"👤 {video['channelTitle']}\n"
                f"🔗 https://youtu.be/{video['id']}",
                reply_markup=reply_markup
            )
    except Exception as e:
        logger.error(f"Ошибка при поиске видео: {e}")
        await update.message.reply_text(f"Произошла ошибка при поиске: {str(e)}")

async def process_video_link(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str) -> None:
    """Обработка ссылки на видео"""
    status_message = await update.message.reply_text(f"🎬 Обрабатываю видео с ID: {video_id}")

    try:
        # Получаем информацию только через YouTube API
        from src.scrapers.youtube_api.youtube_api import get_video_details
        video_details = get_video_details(video_id)

        if video_details:
            # Обновляем статус
            await status_message.edit_text(
                f"🎬 Видео найдено: {video_details.get('title', 'Неизвестное название')}\n"
                f"👤 {video_details.get('channelTitle', 'Неизвестный канал')}\n"
                f"🔗 https://youtu.be/{video_id}\n\n"
                f"⬇️ Начинаю скачивание аудио..."
            )

            # Автоматически запускаем скачивание
            await download_video_auto(update, context, video_id, status_message)
        else:
            # Если не удалось получить информацию через API
            await status_message.edit_text(
                f"⚠️ Не удалось получить информацию о видео: https://youtu.be/{video_id}\n"
                f"Пробую скачать напрямую..."
            )
            # Все равно пробуем скачать
            await download_video_auto(update, context, video_id, status_message)
    except Exception as e:
        logger.error(f"Ошибка при обработке видео: {e}")
        await status_message.edit_text(f"❌ Произошла ошибка при обработке видео: {str(e)}")

async def process_playlist_link(update: Update, context: ContextTypes.DEFAULT_TYPE, playlist_id: str) -> None:
    """Обработка ссылки на плейлист"""
    await update.message.reply_text(f"📋 Получаю список видео из плейлиста: {playlist_id}...")

    try:
        videos = get_playlist_videos(playlist_id, max_results=5)
        if not videos:
            await update.message.reply_text("Плейлист пуст или недоступен.")
            return

        await update.message.reply_text(f"📋 Найдено {len(videos)} видео в плейлисте:")

        for video in videos:
            keyboard = [
                [
                    InlineKeyboardButton("⬇️ Скачать", callback_data=f"download_{video['id']}"),
                    InlineKeyboardButton("ℹ️ Подробнее", callback_data=f"details_{video['id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"🎬 {video['title']}\n"
                f"👤 {video['channelTitle']}\n"
                f"🔗 https://youtu.be/{video['id']}",
                reply_markup=reply_markup
            )
    except Exception as e:
        logger.error(f"Ошибка при получении плейлиста: {e}")
        await update.message.reply_text(f"Произошла ошибка при получении плейлиста: {str(e)}")

async def process_channel_link(update: Update, context: ContextTypes.DEFAULT_TYPE, channel_id: str) -> None:
    """Обработка ссылки на канал"""
    await update.message.reply_text(f"📺 Получаю список видео с канала: {channel_id}...")

    try:
        videos = get_channel_videos(channel_id, max_results=5)
        if not videos:
            await update.message.reply_text("На канале нет видео или канал недоступен.")
            return

        await update.message.reply_text(f"📺 Найдено {len(videos)} видео на канале:")

        for video in videos:
            keyboard = [
                [
                    InlineKeyboardButton("⬇️ Скачать", callback_data=f"download_{video['id']}"),
                    InlineKeyboardButton("ℹ️ Подробнее", callback_data=f"details_{video['id']}")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                f"🎬 {video['title']}\n"
                f"👤 {video['channelTitle']}\n"
                f"🔗 https://youtu.be/{video['id']}",
                reply_markup=reply_markup
            )
    except Exception as e:
        logger.error(f"Ошибка при получении видео с канала: {e}")
        await update.message.reply_text(f"Произошла ошибка при получении видео с канала: {str(e)}")

async def button_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Обработчик нажатий на кнопки"""
    query = update.callback_query
    await query.answer()

    data = query.data

    if data.startswith("download_"):
        video_id = data.split("_")[1]
        await download_video(update, context, video_id)
    elif data.startswith("details_"):
        video_id = data.split("_")[1]
        await show_video_details(update, context, video_id)
    elif data.startswith("transcribe:"):
        # Используем новый разделитель ":" вместо "_"
        video_id = data.split(":", 1)[1]  # Берем все после первого ":"

        # Определяем путь к аудиофайлу на основе video_id
        output_dir = os.path.join(PROJECT_ROOT, "downloads", video_id)

        # Ищем аудиофайл в директории
        audio_path = None
        audio_extensions = [".mp3", ".m4a", ".aac", ".ogg", ".opus", ".wav", ".flac", ".webm"]
        for ext in audio_extensions:
            potential_file = os.path.join(output_dir, f"{video_id}{ext}")
            if os.path.exists(potential_file):
                audio_path = potential_file
                break

        if not audio_path:
            await query.message.reply_text(f"❌ Не удалось найти аудиофайл для видео {video_id}")
            return

        await transcribe_video(update, context, video_id, audio_path)

async def download_video(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str) -> None:
    """Скачивание аудио из видео (через кнопку)"""
    query = update.callback_query
    await query.edit_message_text(f"⬇️ Скачиваю аудио из видео: {video_id}...")

    try:
        # Создаем директорию для загрузок, если она не существует
        output_dir = os.path.join(PROJECT_ROOT, "downloads", video_id)
        os.makedirs(output_dir, exist_ok=True)

        # Скачиваем видео
        audio_path, metadata = downloader.download_video(
            video_id_or_url=video_id,
            output_path=output_dir
        )

        # Отправляем информацию о скачанном аудио
        # Используем префикс и разделитель, чтобы избежать проблем с video_id, содержащим "_"
        keyboard = [
            [
                InlineKeyboardButton("🎤 Транскрибировать", callback_data=f"transcribe:{video_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.message.reply_text(
            f"✅ Аудио успешно скачано!\n\n"
            f"🎬 {metadata.get('title', 'Неизвестное название')}\n"
            f"👤 {metadata.get('uploader', 'Неизвестный автор')}\n"
            f"⏱️ Длительность: {metadata.get('duration_string', 'Неизвестно')}\n\n"
            f"Файл сохранен в: {audio_path}",
            reply_markup=reply_markup
        )

        # Не отправляем аудиофайл пользователю, только информацию о нем
        logger.info(f"Аудиофайл успешно скачан: {audio_path}")
    except Exception as e:
        logger.error(f"Ошибка при скачивании видео: {e}")
        await query.message.reply_text(f"Произошла ошибка при скачивании: {str(e)}")

async def download_video_auto(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str, status_message) -> None:
    """Автоматическое скачивание аудио из видео с последующей транскрибацией"""
    try:
        # Создаем директорию для загрузок, если она не существует
        output_dir = os.path.join(PROJECT_ROOT, "downloads", video_id)
        os.makedirs(output_dir, exist_ok=True)

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"📂 Создана директория для загрузки: {output_dir}"
        )

        # Проверяем, существует ли уже скачанный файл
        existing_audio_file = None
        audio_extensions = [".mp3", ".m4a", ".aac", ".ogg", ".opus", ".wav", ".flac", ".webm"]
        for ext in audio_extensions:
            potential_file = os.path.join(output_dir, f"{video_id}{ext}")
            if os.path.exists(potential_file):
                existing_audio_file = potential_file
                break

        if existing_audio_file:
            # Если файл уже существует, используем его
            audio_path = existing_audio_file
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"🔍 Найден существующий аудиофайл: {os.path.basename(audio_path)}"
            )

            # Получаем метаданные из JSON-файла, если он существует
            metadata_path = os.path.join(output_dir, f"{video_id}_metadata.json")
            if os.path.exists(metadata_path):
                with open(metadata_path, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
            else:
                # Если метаданных нет, получаем их через API
                from src.scrapers.youtube_api.youtube_api import get_video_details
                video_details = get_video_details(video_id)
                if video_details:
                    metadata = {
                        'title': video_details.get('title', 'Неизвестное название'),
                        'uploader': video_details.get('channelTitle', 'Неизвестный автор'),
                        'duration_string': format_duration(video_details.get('duration', 'PT0S')),
                        'duration': parse_duration(video_details.get('duration', 'PT0S'))
                    }
                else:
                    metadata = {
                        'title': f'Видео {video_id}',
                        'uploader': 'Неизвестный автор',
                        'duration_string': 'Неизвестно',
                        'duration': 0
                    }
        else:
            # Скачиваем видео
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"⬇️ Скачиваю аудио... (это может занять некоторое время)"
            )

            try:
                audio_path, metadata = downloader.download_video(
                    video_id_or_url=video_id,
                    output_path=output_dir
                )

                # Обновляем статус
                await status_message.edit_text(
                    f"{status_message.text}\n"
                    f"✅ Аудио успешно скачано!"
                )
            except Exception as download_error:
                logger.error(f"Ошибка при скачивании видео: {download_error}")
                await status_message.edit_text(
                    f"{status_message.text}\n"
                    f"⚠️ Ошибка при скачивании: {str(download_error)}\n"
                    f"Проверяю, был ли файл скачан частично..."
                )

                # Проверяем, был ли файл скачан частично
                for ext in audio_extensions:
                    potential_file = os.path.join(output_dir, f"{video_id}{ext}")
                    if os.path.exists(potential_file):
                        audio_path = potential_file
                        await status_message.edit_text(
                            f"{status_message.text}\n"
                            f"🔍 Найден частично скачанный файл: {os.path.basename(audio_path)}"
                        )

                        # Получаем базовые метаданные
                        from src.scrapers.youtube_api.youtube_api import get_video_details
                        video_details = get_video_details(video_id)
                        if video_details:
                            metadata = {
                                'title': video_details.get('title', 'Неизвестное название'),
                                'uploader': video_details.get('channelTitle', 'Неизвестный автор'),
                                'duration_string': format_duration(video_details.get('duration', 'PT0S')),
                                'duration': parse_duration(video_details.get('duration', 'PT0S'))
                            }
                        else:
                            metadata = {
                                'title': f'Видео {video_id}',
                                'uploader': 'Неизвестный автор',
                                'duration_string': 'Неизвестно',
                                'duration': 0
                            }
                        break
                else:
                    # Если файл не найден, выходим с ошибкой
                    await status_message.edit_text(
                        f"{status_message.text}\n"
                        f"❌ Не удалось скачать аудио. Пожалуйста, попробуйте позже."
                    )
                    return

        # Отправляем информацию о скачанном аудио
        info_message = await update.message.reply_text(
            f"✅ Аудио {'успешно скачано' if not existing_audio_file else 'найдено в кэше'}!\n\n"
            f"🎬 {metadata.get('title', 'Неизвестное название')}\n"
            f"👤 {metadata.get('uploader', 'Неизвестный автор')}\n"
            f"⏱️ Длительность: {metadata.get('duration_string', 'Неизвестно')}\n\n"
            f"Файл сохранен в: {audio_path}"
        )

        # Не отправляем аудиофайл пользователю, только информацию о нем
        logger.info(f"Аудиофайл успешно скачан: {audio_path}")

        # Спрашиваем пользователя, хочет ли он транскрибировать аудио
        # Используем префикс и разделитель, чтобы избежать проблем с video_id, содержащим "_"
        keyboard = [
            [
                InlineKeyboardButton("🎤 Транскрибировать", callback_data=f"transcribe:{video_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "Хотите транскрибировать аудио?",
            reply_markup=reply_markup
        )

        # Обновляем финальный статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"✅ Процесс скачивания завершен. Для транскрибации нажмите кнопку 'Транскрибировать'."
        )

    except Exception as e:
        logger.error(f"Ошибка при автоматическом скачивании видео: {e}")
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"❌ Произошла ошибка: {str(e)}"
        )

async def show_video_details(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str) -> None:
    """Показ подробной информации о видео"""
    query = update.callback_query
    await query.edit_message_text(f"ℹ️ Получаю информацию о видео: {video_id}...")

    try:
        # Получаем информацию только через YouTube API
        from src.scrapers.youtube_api.youtube_api import get_video_details
        video_details = get_video_details(video_id)

        if not video_details:
            await query.message.reply_text("Не удалось получить информацию о видео через YouTube API.")
            return

        # Форматируем и отправляем информацию из YouTube API
        # Преобразуем дату публикации в более читаемый формат
        published_at = video_details.get('publishedAt', 'Неизвестно')
        if published_at and published_at != 'Неизвестно':
            try:
                # Формат даты в API: 2023-01-15T12:30:45Z
                date_parts = published_at.split('T')[0].split('-')
                if len(date_parts) == 3:
                    published_at = f"{date_parts[2]}.{date_parts[1]}.{date_parts[0]}"
            except:
                pass

        # Форматируем числа для лучшей читаемости
        view_count = video_details.get('viewCount', 'Неизвестно')
        if view_count and view_count != 'Неизвестно':
            try:
                view_count = f"{int(view_count):,}".replace(',', ' ')
            except:
                pass

        like_count = video_details.get('likeCount', 'Неизвестно')
        if like_count and like_count != 'Неизвестно':
            try:
                like_count = f"{int(like_count):,}".replace(',', ' ')
            except:
                pass

        # Форматируем длительность
        duration = video_details.get('duration', 'Неизвестно')
        duration_str = 'Неизвестно'
        if duration and duration != 'Неизвестно':
            try:
                # Формат PT1H30M15S
                duration = duration.replace('PT', '')
                hours = 0
                minutes = 0
                seconds = 0

                if 'H' in duration:
                    hours_part = duration.split('H')[0]
                    duration = duration.split('H')[1]
                    hours = int(hours_part)

                if 'M' in duration:
                    minutes_part = duration.split('M')[0]
                    duration = duration.split('M')[1]
                    minutes = int(minutes_part)

                if 'S' in duration:
                    seconds_part = duration.split('S')[0]
                    seconds = int(seconds_part)

                if hours > 0:
                    duration_str = f"{hours}:{minutes:02d}:{seconds:02d}"
                else:
                    duration_str = f"{minutes}:{seconds:02d}"
            except:
                duration_str = duration

        details_text = (
            f"🎬 {video_details.get('title', 'Неизвестное название')}\n\n"
            f"👤 Канал: {video_details.get('channelTitle', 'Неизвестный канал')}\n"
            f"📅 Дата публикации: {published_at}\n"
            f"⏱️ Длительность: {duration_str}\n"
            f"👁️ Просмотры: {view_count}\n"
            f"👍 Лайки: {like_count}\n\n"
            f"📝 Описание:\n{video_details.get('description', 'Описание отсутствует')[:500]}..."
        )

        keyboard = [
            [
                InlineKeyboardButton("⬇️ Скачать аудио", callback_data=f"download_{video_id}")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.message.reply_text(details_text, reply_markup=reply_markup)
    except Exception as e:
        logger.error(f"Ошибка при получении информации о видео: {e}")
        await query.message.reply_text(f"Произошла ошибка при получении информации: {str(e)}")

async def transcribe_video(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str, audio_path: str) -> None:
    """Транскрибация аудио (через кнопку)"""
    query = update.callback_query

    # Сначала изменяем текст сообщения, чтобы пользователь знал, что процесс начался
    await query.edit_message_text(f"🎤 Транскрибирую аудио из видео: {video_id}...")

    # Отправляем новое сообщение о начале процесса транскрибации
    status_message = await query.message.reply_text(
        f"⏳ Начинаю транскрибацию аудио из видео {video_id}...\n"
        f"Это может занять некоторое время, пожалуйста, подождите."
    )

    try:
        # Получаем API ключ для Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Ошибка: GEMINI_API_KEY не найден в переменных окружения."
            )
            return

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"🔍 Анализирую аудио и готовлю транскрипцию... (это может занять несколько минут)"
        )

        # Транскрибируем аудио
        transcript, token_info = transcribe_audio(
            api_key=api_key,
            audio_file_path=audio_path,
            with_timestamps=True,
            use_prompt=True,
            language='ru'  # Можно сделать выбор языка через интерфейс бота
        )

        if not transcript:
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Не удалось транскрибировать аудио."
            )
            return

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"💾 Сохраняю транскрипцию в файлы..."
        )

        # Сохраняем транскрипцию в файл
        transcript_file_path = os.path.join(os.path.dirname(audio_path), f"{video_id}_transcript.txt")
        markdown_file_path = os.path.join(os.path.dirname(audio_path), f"{video_id}_transcript.md")

        # Сохраняем в txt формате
        with open(transcript_file_path, 'w', encoding='utf-8') as f:
            f.write(token_info)
            f.write(transcript)

        # Сохраняем в markdown формате
        with open(markdown_file_path, 'w', encoding='utf-8') as f:
            f.write("# Транскрипция видео\n\n")
            f.write(f"**Видео ID:** {video_id}\n\n")
            f.write("## Содержание\n\n")
            f.write(transcript)

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"✅ Транскрипция готова! Отправляю результаты..."
        )

        # Отправляем краткую часть транскрипции в сообщении
        max_message_length = 1000  # Уменьшаем длину для краткости
        preview_text = transcript[:max_message_length] + (
            "...\n\n(Полная версия доступна в прикрепленных файлах)" if len(transcript) > max_message_length else ""
        )

        await update.effective_chat.send_message(f"✅ Транскрипция готова!\n\n{preview_text}")

        # Отправляем файлы с транскрипцией
        try:
            with open(markdown_file_path, 'rb') as f:
                await update.effective_chat.send_document(
                    document=f,
                    filename=f"{video_id}_transcript.md",
                    caption="Полная транскрипция в Markdown формате"
                )

            with open(transcript_file_path, 'rb') as f:
                await update.effective_chat.send_document(
                    document=f,
                    filename=f"{video_id}_transcript.txt",
                    caption="Полная транскрипция в текстовом файле"
                )

            # Обновляем финальный статус
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"✅ Транскрипция успешно отправлена!"
            )
        except Exception as send_error:
            logger.error(f"Ошибка при отправке файлов транскрипции: {send_error}")
            await update.effective_chat.send_message(
                f"⚠️ Не удалось отправить файлы транскрипции: {str(send_error)}\n"
                f"Но вы можете найти их по путям:\n"
                f"- TXT: {transcript_file_path}\n"
                f"- MD: {markdown_file_path}"
            )
    except Exception as e:
        logger.error(f"Ошибка при транскрибации: {e}")
        try:
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Произошла ошибка при транскрибации: {str(e)}"
            )
        except:
            # Если не удалось обновить статусное сообщение, отправляем новое
            await update.effective_chat.send_message(f"❌ Произошла ошибка при транскрибации: {str(e)}")

async def transcribe_video_auto(update: Update, context: ContextTypes.DEFAULT_TYPE, video_id: str, audio_path: str, status_message) -> None:
    """Автоматическая транскрибация аудио"""
    # Проверяем, существует ли уже транскрипция
    transcript_file_path = os.path.join(os.path.dirname(audio_path), f"{video_id}_transcript.txt")
    if os.path.exists(transcript_file_path):
        try:
            # Если файл транскрипции уже существует, используем его
            with open(transcript_file_path, 'r', encoding='utf-8') as f:
                transcript = f.read()

            # Обновляем статус
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"🔍 Найдена существующая транскрипция!"
            )

            # Проверяем, существует ли markdown файл
            markdown_file_path = os.path.join(os.path.dirname(audio_path), f"{video_id}_transcript.md")
            if not os.path.exists(markdown_file_path):
                # Создаем markdown файл
                with open(markdown_file_path, 'w', encoding='utf-8') as f:
                    f.write("# Транскрипция видео\n\n")
                    f.write(f"**Видео ID:** {video_id}\n\n")
                    f.write("## Содержание\n\n")
                    f.write(transcript)

            # Отправляем сообщение с результатами
            await update.message.reply_text(f"✅ Найдена существующая транскрипция для видео {video_id}!")

            # Отправляем краткую часть транскрипции в сообщении
            max_message_length = 1000  # Уменьшаем длину для краткости
            preview_text = transcript[:max_message_length] + (
                "...\n\n(Полная версия доступна в прикрепленных файлах)" if len(transcript) > max_message_length else ""
            )

            await update.message.reply_text(f"📝 Транскрипция:\n\n{preview_text}")

            # Отправляем файлы с транскрипцией
            with open(markdown_file_path, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=f"{video_id}_transcript.md",
                    caption="Полная транскрипция в Markdown формате"
                )

            with open(transcript_file_path, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=f"{video_id}_transcript.txt",
                    caption="Полная транскрипция в текстовом файле"
                )

            # Обновляем финальный статус
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"✅ Транскрипция успешно отправлена!"
            )
            return
        except Exception as e:
            logger.error(f"Ошибка при чтении существующей транскрипции: {e}")
            # Продолжаем с созданием новой транскрипции

    # Если существующей транскрипции нет или произошла ошибка при ее чтении, создаем новую
    try:
        # Получаем API ключ для Gemini
        api_key = os.getenv('GEMINI_API_KEY')
        if not api_key:
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Ошибка: GEMINI_API_KEY не найден в переменных окружения."
            )
            return

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"🔍 Анализирую аудио и готовлю транскрипцию... (это может занять несколько минут)"
        )

        # Проверяем существование аудиофайла
        if not os.path.exists(audio_path):
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Ошибка: Аудиофайл не найден по пути {audio_path}"
            )
            return

        # Транскрибируем аудио
        try:
            transcript, token_info = transcribe_audio(
                api_key=api_key,
                audio_file_path=audio_path,
                with_timestamps=True,
                use_prompt=True,
                language='ru'
            )
        except Exception as transcribe_error:
            logger.error(f"Ошибка при транскрибации: {transcribe_error}")
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Ошибка при транскрибации: {str(transcribe_error)}"
            )
            return

        if not transcript:
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"❌ Не удалось транскрибировать аудио. Возможно, файл слишком большой или в неподдерживаемом формате."
            )
            return

        # Обновляем статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"💾 Сохраняю транскрипцию в файл..."
        )

        # Создаем пути для файлов
        markdown_file_path = os.path.join(os.path.dirname(audio_path), f"{video_id}_transcript.md")

        # Сохраняем транскрипцию в файлы
        try:
            # Сохраняем в txt формате
            with open(transcript_file_path, 'w', encoding='utf-8') as f:
                if token_info:
                    f.write(token_info)
                f.write(transcript)

            # Сохраняем в markdown формате
            with open(markdown_file_path, 'w', encoding='utf-8') as f:
                f.write("# Транскрипция видео\n\n")
                f.write(f"**Видео ID:** {video_id}\n\n")
                f.write("## Содержание\n\n")
                f.write(transcript)
        except Exception as save_error:
            logger.error(f"Ошибка при сохранении транскрипции: {save_error}")
            await status_message.edit_text(
                f"{status_message.text}\n"
                f"⚠️ Ошибка при сохранении транскрипции в файл: {str(save_error)}"
            )
            # Продолжаем, так как транскрипция все равно получена

        # Обновляем финальный статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"✅ Транскрипция готова! Отправляю результаты..."
        )

        # Отправляем сообщение с результатами
        await update.message.reply_text(f"✅ Транскрипция видео {video_id} завершена!")

        # Отправляем краткую часть транскрипции в сообщении
        max_message_length = 1000  # Уменьшаем длину для краткости
        preview_text = transcript[:max_message_length] + (
            "...\n\n(Полная версия доступна в прикрепленных файлах)" if len(transcript) > max_message_length else ""
        )

        await update.message.reply_text(f"📝 Транскрипция:\n\n{preview_text}")

        # Отправляем файлы с транскрипцией
        try:
            with open(markdown_file_path, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=f"{video_id}_transcript.md",
                    caption="Полная транскрипция в Markdown формате"
                )

            with open(transcript_file_path, 'rb') as f:
                await update.message.reply_document(
                    document=f,
                    filename=f"{video_id}_transcript.txt",
                    caption="Полная транскрипция в текстовом файле"
                )
        except Exception as send_error:
            logger.error(f"Ошибка при отправке файла транскрипции: {send_error}")
            await update.message.reply_text(
                f"⚠️ Не удалось отправить файлы транскрипции: {str(send_error)}\n"
                f"Но вы можете найти их по путям:\n"
                f"- TXT: {transcript_file_path}\n"
                f"- MD: {markdown_file_path}"
            )

        # Обновляем финальный статус
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"✅ Транскрипция успешно отправлена!"
        )
    except Exception as e:
        logger.error(f"Ошибка при автоматической транскрибации: {e}")
        await status_message.edit_text(
            f"{status_message.text}\n"
            f"❌ Произошла ошибка при транскрибации: {str(e)}"
        )