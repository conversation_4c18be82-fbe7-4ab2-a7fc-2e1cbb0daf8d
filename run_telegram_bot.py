#!/usr/bin/env python
"""
Скрипт для запуска Telegram-бота YouTube Downloader
"""

import logging
import asyncio
from src.telegram.bot import (
    Application, CommandHandler, MessageHandler, CallbackQueryHandler,
    filters, start, help_command, search_command, handle_message, button_callback,
    TELEGRAM_BOT_TOKEN
)

# Настройка логирования
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

async def setup_application():
    """Настройка приложения"""
    # Создаем приложение
    application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()

    # Добавляем обработчики команд
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(CommandHandler("search", search_command))

    # Добавляем обработчик сообщений
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_message))

    # Добавляем обработчик кнопок
    application.add_handler(CallbackQueryHandler(button_callback))

    return application

def main():
    """Запуск бота"""
    try:
        # Создаем новый цикл событий
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Инициализируем приложение
        print("Инициализация Telegram-бота YouTube Downloader...")
        application = loop.run_until_complete(setup_application())

        # Запускаем бота
        print("Запуск Telegram-бота YouTube Downloader...")
        application.run_polling(allowed_updates=["message", "callback_query"])
    except Exception as e:
        logger.error(f"Ошибка при запуске бота: {e}")
        raise

if __name__ == "__main__":
    main()