from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Optional

@dataclass
class TranscriptionResult:
    """
    Представляет результат операции транскрибации.
    """
    text: Optional[str] = None
    token_info: Optional[str] = None  # Может содержать информацию о токенах, специфичную для провайдера
    error_message: Optional[str] = None
    success: bool = False # Флаг успешности операции

class ITranscriptionProvider(ABC):
    """
    Абстрактный базовый класс для всех провайдеров транскрибации.
    """

    @abstractmethod
    def transcribe(
        self, 
        audio_file_path: str, 
        language: Optional[str] = None,
        prompt_text: Optional[str] = None,
        with_timestamps: bool = False,
        **kwargs 
    ) -> TranscriptionResult:
        """
        Выполняет транскрибацию аудиофайла.

        Args:
            audio_file_path: Путь к аудиофайлу для транскрибации (ожидается формат, поддерживаемый провайдером, например, MP3, WAV).
            language: Код языка для транскрибации (например, 'ru', 'en').
            prompt_text: Текст промпта для улучшения качества транскрибации.
            with_timestamps: Запрашивать ли таймкоды (если поддерживается провайдером).
            **kwargs: Дополнительные параметры, специфичные для провайдера.

        Returns:
            Объект TranscriptionResult с текстом транскрипции и/или сообщением об ошибке.
        """
        pass

    @abstractmethod
    def get_provider_name(self) -> str:
        """
        Возвращает имя провайдера.
        """
        pass 