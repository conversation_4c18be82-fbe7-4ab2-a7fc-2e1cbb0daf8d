# youtube_api.py
import os
import re
from datetime import datetime, timedelta
from dotenv import load_dotenv
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import psycopg2
import psycopg2.extras # Для работы с dict-курсором и адаптации JSON
import json # Для сериализации, если понадобится напрямую

class YouTubeApiClient:
    """
    Класс для работы с YouTube API.
    Предоставляет методы для получения информации о видео, каналах и плейлистах.
    """

    def __init__(self, api_key=None):
        """
        Инициализирует клиент YouTube API.

        Args:
            api_key (str, optional): API ключ для YouTube API. Если не указан,
                                     используется ключ из переменных окружения.
        """
        self.api_key = api_key or os.getenv("YOUTUBE_API_KEY")
        if not self.api_key:
            print("Переменная окружения YOUTUBE_API_KEY не найдена. Пожалуйста, установите ее.")

        self.youtube_service = None
        if self.api_key:
            self.youtube_service = build('youtube', 'v3', developerKey=self.api_key)

    def get_metadata_for_download(self, video_id):
        """
        Получает метаданные видео для загрузки.

        Args:
            video_id (str): ID видео на YouTube.

        Returns:
            dict: Словарь с метаданными видео или None, если видео не найдено.
        """
        video_details = get_video_details(video_id)
        if not video_details:
            return None

        # Преобразуем данные из API в формат, подходящий для загрузчика
        metadata = {
            'id': video_details.get('id'),
            'title': video_details.get('title'),
            'uploader': video_details.get('channelTitle'),
            'channel_id': video_details.get('channelId'),
            'upload_date': video_details.get('publishedAt', '').split('T')[0].replace('-', ''),
            'description': video_details.get('description'),
            'duration': video_details.get('duration'),
            'duration_string': format_duration(video_details.get('duration')),
            'view_count': video_details.get('viewCount'),
            'like_count': video_details.get('likeCount'),
            'comment_count': video_details.get('commentCount'),
            'thumbnail': video_details.get('thumbnails', {}).get('high', {}).get('url'),
            'webpage_url': f'https://www.youtube.com/watch?v={video_id}'
        }

        return metadata

# Загружаем переменные окружения
load_dotenv()

# Получаем API ключ из переменных окружения
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")

# Параметры подключения к базе данных из переменных окружения
DB_HOST = os.getenv("DB_HOST")
DB_PORT = os.getenv("DB_PORT", "5432") # Порт по умолчанию, если не указан
DB_NAME = os.getenv("DB_NAME")
DB_USER = os.getenv("DB_USER")
DB_PASS = os.getenv("DB_PASSWORD")

if not YOUTUBE_API_KEY:
    print("Переменная окружения YOUTUBE_API_KEY не найдена. Пожалуйста, установите ее.")
    # raise ValueError("YOUTUBE_API_KEY is not set in the environment variables.")

def get_db_connection():
    """Устанавливает соединение с базой данных PostgreSQL."""
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASS
        )
        return conn
    except psycopg2.Error as e:
        print(f"Ошибка подключения к базе данных PostgreSQL: {e}")
        # Можно пробросить исключение дальше или вернуть None, в зависимости от стратегии обработки ошибок
        raise # Пробрасываем, чтобы вызывающий код мог это обработать

def save_video_data_to_db(video_data_json):
    """
    Сохраняет или обновляет JSON-данные видео в таблице youtube_videos_api.
    Извлекает основные поля из JSON и сохраняет их в отдельные колонки для быстрого доступа.

    Args:
        video_data_json - словарь, представляющий JSON объекта видео из API.
    """
    if not video_data_json or 'id' not in video_data_json:
        print("Ошибка: неверные данные видео для сохранения.")
        return

    video_id = video_data_json['id']

    # Извлекаем нужные данные из JSON
    title = video_data_json.get('snippet', {}).get('title', '')
    description = video_data_json.get('snippet', {}).get('description', '')
    published_at = video_data_json.get('snippet', {}).get('publishedAt')

    # Получаем URL миниатюры максимального разрешения (если есть) или следующего доступного размера
    thumbnails = video_data_json.get('snippet', {}).get('thumbnails', {})
    if 'maxres' in thumbnails:
        thumbnail_url = thumbnails['maxres'].get('url')
    elif 'standard' in thumbnails:
        thumbnail_url = thumbnails['standard'].get('url')
    elif 'high' in thumbnails:
        thumbnail_url = thumbnails['high'].get('url')
    elif 'medium' in thumbnails:
        thumbnail_url = thumbnails['medium'].get('url')
    elif 'default' in thumbnails:
        thumbnail_url = thumbnails['default'].get('url')
    else:
        thumbnail_url = None

    # Данные о канале
    channel_id = video_data_json.get('snippet', {}).get('channelId')
    channel_title = video_data_json.get('snippet', {}).get('channelTitle')

    # Статистика
    statistics = video_data_json.get('statistics', {})
    view_count = int(statistics.get('viewCount', 0)) if statistics.get('viewCount') else 0
    like_count = int(statistics.get('likeCount', 0)) if statistics.get('likeCount') else 0
    comment_count = int(statistics.get('commentCount', 0)) if statistics.get('commentCount') else 0

    # Длительность и другие метаданные контента
    duration = video_data_json.get('contentDetails', {}).get('duration')
    duration_seconds = parse_duration(duration) if duration else 0
    duration_formatted = format_duration(duration) if duration else ''
    category_id = video_data_json.get('snippet', {}).get('categoryId')

    # Извлекаем теги как массив
    tags_array = video_data_json.get('snippet', {}).get('tags', [])

    # Доступность субтитров
    is_caption_available = video_data_json.get('contentDetails', {}).get('caption') == 'true'

    # Возрастные ограничения и монетизация
    is_age_restricted = video_data_json.get('contentDetails', {}).get('contentRating', {}) != {}
    is_monetized = video_data_json.get('contentDetails', {}).get('licensedContent', False)

    # Темы видео
    topic_categories = video_data_json.get('topicDetails', {}).get('topicCategories', [])

    sql = """
    INSERT INTO youtube_videos_api (
        video_id, data, retrieved_at,
        title, description, published_at,
        thumbnail_url, channel_id, channel_title,
        view_count, like_count, comment_count,
        duration, duration_seconds, duration_formatted, category_id, tags,
        is_age_restricted, is_monetized, is_caption_available,
        topic_categories
    )
    VALUES (
        %s, %s, CURRENT_TIMESTAMP,
        %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s, %s, %s,
        %s, %s, %s,
        %s
    )
    ON CONFLICT (video_id) DO UPDATE SET
        data = EXCLUDED.data,
        retrieved_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP,
        title = EXCLUDED.title,
        description = EXCLUDED.description,
        published_at = EXCLUDED.published_at,
        thumbnail_url = EXCLUDED.thumbnail_url,
        channel_id = EXCLUDED.channel_id,
        channel_title = EXCLUDED.channel_title,
        view_count = EXCLUDED.view_count,
        like_count = EXCLUDED.like_count,
        comment_count = EXCLUDED.comment_count,
        duration = EXCLUDED.duration,
        duration_seconds = EXCLUDED.duration_seconds,
        duration_formatted = EXCLUDED.duration_formatted,
        category_id = EXCLUDED.category_id,
        tags = EXCLUDED.tags,
        is_age_restricted = EXCLUDED.is_age_restricted,
        is_monetized = EXCLUDED.is_monetized,
        is_caption_available = EXCLUDED.is_caption_available,
        topic_categories = EXCLUDED.topic_categories;
    """

    conn = None
    try:
        conn = get_db_connection()
        if conn:
            with conn.cursor() as cur:
                cur.execute(sql, (
                    video_id,
                    psycopg2.extras.Json(video_data_json),
                    title,
                    description,
                    published_at,
                    thumbnail_url,
                    channel_id,
                    channel_title,
                    view_count,
                    like_count,
                    comment_count,
                    duration,
                    duration_seconds,
                    duration_formatted,
                    category_id,
                    psycopg2.extras.Json(tags_array),
                    is_age_restricted,
                    is_monetized,
                    is_caption_available,
                    psycopg2.extras.Json(topic_categories)
                ))
                conn.commit()
                print(f"Видео {video_id} успешно сохранено/обновлено в БД.")
    except psycopg2.Error as e:
        print(f"Ошибка при сохранении видео {video_id} в БД: {e}")
        if conn:
            conn.rollback() # Откатываем транзакцию при ошибке
        # Можно пробросить исключение, если это необходимо для логики вызывающего кода
    except Exception as e:
        print(f"Непредвиденная ошибка при сохранении видео {video_id} в БД: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

def get_youtube_service():
    """
    Создает и возвращает объект сервиса YouTube API.

    Returns:
        Объект сервиса YouTube API.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
    """
    if not YOUTUBE_API_KEY:
        raise ValueError("API ключ YouTube не установлен. Поиск видео невозможен.")

    return build('youtube', 'v3', developerKey=YOUTUBE_API_KEY)

def parse_duration(duration_iso):
    """
    Преобразует строку длительности в формате ISO 8601 (PT1H2M3S) в секунды.

    Args:
        duration_iso (str): Строка с длительностью в формате ISO 8601.

    Returns:
        int: Количество секунд.
    """
    if not duration_iso or not duration_iso.startswith('PT'):
        return 0

    # Извлекаем часы, минуты, секунды с помощью регулярных выражений
    hours = re.search(r'(\d+)H', duration_iso)
    minutes = re.search(r'(\d+)M', duration_iso)
    seconds = re.search(r'(\d+)S', duration_iso)

    total_seconds = 0

    if hours:
        total_seconds += int(hours.group(1)) * 3600
    if minutes:
        total_seconds += int(minutes.group(1)) * 60
    if seconds:
        total_seconds += int(seconds.group(1))

    return total_seconds

def format_duration(duration_iso):
    """
    Преобразует длительность в формате ISO 8601 (PT1H2M3S) в читаемый формат (1:02:03).

    Args:
        duration_iso (str): Строка с длительностью в формате ISO 8601.

    Returns:
        str: Отформатированная строка времени.
    """
    total_seconds = parse_duration(duration_iso)

    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60

    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def search_videos(query, max_results=10, region_code="RU", relevance_language="ru",
                 order="relevance", video_type=None, video_category=None, published_after=None, published_before=None,
                 fetch_and_save_new_channels=False):
    """
    Выполняет поиск видео на YouTube по заданному запросу.
    Сохраняет полученные детальные данные о видео в БД.

    Args:
        query (str): Поисковый запрос.
        max_results (int, optional): Максимальное количество результатов. По умолчанию 10.
        region_code (str, optional): Код региона для поиска. По умолчанию "RU".
        relevance_language (str, optional): Язык для релевантности. По умолчанию "ru".
        order (str, optional): Порядок сортировки результатов.
            Возможные значения: "date", "rating", "relevance", "title", "videoCount", "viewCount".
            По умолчанию "relevance".
        video_type (str, optional): Тип видео.
            Возможные значения: "episode", "movie", "any".
        video_category (str, optional): ID категории видео.

    Returns:
        list: Список словарей с информацией о найденных видео.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()

        search_params = {
            'q': query,
            'type': 'video',
            'part': 'snippet',
            'maxResults': max_results,
            'regionCode': region_code,
            'relevanceLanguage': relevance_language,
            'order': order
        }

        if video_type:
            search_params['videoType'] = video_type
        if video_category:
            search_params['videoCategoryId'] = video_category
        if published_after:
            search_params['publishedAfter'] = published_after
        if published_before:
            search_params['publishedBefore'] = published_before

        search_response = youtube.search().list(**search_params).execute()
        video_ids = [item['id']['videoId'] for item in search_response.get('items', [])]

        if not video_ids:
            return []

        videos_response = youtube.videos().list(
            part='snippet,contentDetails,statistics,status,player,topicDetails,recordingDetails',
            id=','.join(video_ids)
        ).execute()

        processed_videos_for_return = []
        unique_channel_ids_to_process = set()

        if 'items' in videos_response:
            for video_item_json in videos_response['items']:
                try:
                    save_video_data_to_db(video_item_json)

                    if fetch_and_save_new_channels: # Проверяем флаг
                        channel_id = video_item_json.get('snippet', {}).get('channelId')
                        if channel_id:
                            unique_channel_ids_to_process.add(channel_id)

                    video_id_for_url = video_item_json['id']
                    processed_video_info = {
                        'id': video_id_for_url,
                        'title': video_item_json['snippet']['title'],
                        'description': video_item_json['snippet']['description'],
                        'thumbnails': video_item_json['snippet']['thumbnails'],
                        'published_at': video_item_json['snippet']['publishedAt'],
                        'channel_id': video_item_json.get('snippet', {}).get('channelId'),
                        'channel_title': video_item_json['snippet']['channelTitle'],
                        'url': f'https://www.youtube.com/watch?v={video_id_for_url}'
                    }
                    if 'contentDetails' in video_item_json:
                        processed_video_info['duration'] = video_item_json['contentDetails']['duration']
                    if 'statistics' in video_item_json:
                        processed_video_info['statistics'] = video_item_json['statistics']
                    processed_videos_for_return.append(processed_video_info)

                except Exception as e_save_video:
                    print(f"Ошибка при попытке сохранить видео {video_item_json.get('id', 'UNKNOWN_ID')} или собрать ID канала: {e_save_video}")

            if fetch_and_save_new_channels and unique_channel_ids_to_process:
                conn_check = None
                try:
                    conn_check = get_db_connection()
                    if conn_check:
                        create_youtube_channels_table(conn_check)
                        with conn_check.cursor() as cur_check:
                            placeholders = ', '.join(['%s'] * len(unique_channel_ids_to_process))
                            cur_check.execute(f"SELECT channel_id FROM youtube_channels WHERE channel_id IN ({placeholders})", tuple(unique_channel_ids_to_process))
                            existing_channel_ids = {row[0] for row in cur_check.fetchall()}

                        channel_ids_to_actually_fetch_details = list(unique_channel_ids_to_process - existing_channel_ids)

                        if channel_ids_to_actually_fetch_details:
                            print(f"Обнаружены новые каналы для сохранения: {channel_ids_to_actually_fetch_details}")
                            for i in range(0, len(channel_ids_to_actually_fetch_details), 50):
                                chunk_channel_ids = channel_ids_to_actually_fetch_details[i:i+50]
                                try:
                                    channel_details_response = youtube.channels().list(
                                        part='snippet,contentDetails,statistics,topicDetails,brandingSettings',
                                        id=','.join(chunk_channel_ids)
                                    ).execute()
                                    for channel_info_item in channel_details_response.get('items', []):
                                        save_channel_details_to_db(channel_info_item)
                                except HttpError as he_channel:
                                    print(f"Ошибка API при получении деталей для каналов {chunk_channel_ids}: {he_channel}")
                                except Exception as e_channel_fetch:
                                    print(f"Непредвиденная ошибка при получении или сохранении деталей для каналов {chunk_channel_ids}: {e_channel_fetch}")
                except psycopg2.Error as db_err:
                    print(f"Ошибка БД при проверке или сохранении каналов: {db_err}")
                except Exception as e_channels_block:
                    print(f"Общая ошибка в блоке обработки каналов: {e_channels_block}")
                finally:
                    if conn_check:
                        conn_check.close()

            return processed_videos_for_return

        return []

    except HttpError as e:
        print(f"Ошибка при выполнении поискового запроса к YouTube API: {e}")
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при поиске видео: {e}")
        raise

def get_channel_details(channel_id):
    """
    Получает информацию о канале YouTube по его ID.

    Args:
        channel_id (str): ID канала YouTube.

    Returns:
        dict: Информация о канале.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()

        # Запрос информации о канале
        channel_response = youtube.channels().list(
            part='snippet,contentDetails,statistics',
            id=channel_id
        ).execute()

        if not channel_response['items']:
            return None

        channel_info = channel_response['items'][0]

        # Извлечение ID плейлиста с загрузками канала
        uploads_playlist_id = channel_info['contentDetails']['relatedPlaylists']['uploads']

        # Сформируем словарь с информацией о канале
        result = {
            'id': channel_info['id'],
            'title': channel_info['snippet']['title'],
            'description': channel_info['snippet']['description'],
            'thumbnails': channel_info['snippet']['thumbnails'],
            'published_at': channel_info['snippet']['publishedAt'],
            'country': channel_info['snippet'].get('country'),
            'uploads_playlist_id': uploads_playlist_id,
            'statistics': {
                'view_count': int(channel_info['statistics'].get('viewCount', 0)),
                'subscriber_count': int(channel_info['statistics'].get('subscriberCount', 0)),
                'video_count': int(channel_info['statistics'].get('videoCount', 0))
            },
            'url': f'https://www.youtube.com/channel/{channel_info["id"]}'
        }

        return result

    except HttpError as e:
        print(f"Ошибка при получении информации о канале: {e}")
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при получении информации о канале: {e}")
        raise

def get_video_details(video_id):
    """
    Получает подробную информацию о видео по его ID.
    Сохраняет полученный JSON в БД.

    Args:
        video_id (str): ID видео на YouTube.

    Returns:
        dict: Словарь с подробной информацией о видео или None, если видео не найдено.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()

        # Запрос информации о видео
        video_response = youtube.videos().list(
            part='snippet,contentDetails,statistics,status,player,topicDetails,recordingDetails',
            id=video_id
        ).execute()

        # Если видео не найдено, возвращаем None
        if not video_response['items']:
            return None

        video_info_json = video_response['items'][0] # Это полный JSON-объект для видео

        # Сохраняем полученный JSON в базу данных
        try:
            save_video_data_to_db(video_info_json)
        except Exception as e_save:
            # Логируем ошибку сохранения, но функция может продолжать работу,
            # возвращая данные, если это допустимо по логике приложения.
            print(f"Ошибка при попытке сохранить видео {video_id} из get_video_details: {e_save}")

        # Формируем результирующий словарь с подробной информацией, как и раньше
        # (на основе video_info_json, который мы только что сохранили)
        result = {
            'id': video_info_json['id'],
            'title': video_info_json['snippet']['title'],
            'description': video_info_json['snippet']['description'],
            'published_at': video_info_json['snippet']['publishedAt'],
            'thumbnails': video_info_json['snippet']['thumbnails'],
            'channel_id': video_info_json['snippet']['channelId'],
            'channel_title': video_info_json['snippet']['channelTitle'],
            'tags': video_info_json['snippet'].get('tags', []),
            'category_id': video_info_json['snippet'].get('categoryId'),
            'live_broadcast_content': video_info_json['snippet'].get('liveBroadcastContent'),
            'url': f'https://www.youtube.com/watch?v={video_info_json["id"]}',

            # ContentDetails
            'duration': video_info_json['contentDetails']['duration'],
            'duration_seconds': parse_duration(video_info_json['contentDetails']['duration']),
            'duration_formatted': format_duration(video_info_json['contentDetails']['duration']),
            'dimension': video_info_json['contentDetails'].get('dimension'),
            'definition': video_info_json['contentDetails'].get('definition'),
            'caption': video_info_json['contentDetails'].get('caption') == 'true',
            'licensed_content': video_info_json['contentDetails'].get('licensedContent', False),
            'projection': video_info_json['contentDetails'].get('projection'),

            # Statistics
            'view_count': int(video_info_json['statistics'].get('viewCount', 0)),
            'like_count': int(video_info_json['statistics'].get('likeCount', 0)),
            'dislike_count': int(video_info_json['statistics'].get('dislikeCount', 0)) if 'dislikeCount' in video_info_json['statistics'] else None,
            'favorite_count': int(video_info_json['statistics'].get('favoriteCount', 0)),
            'comment_count': int(video_info_json['statistics'].get('commentCount', 0)),

            # Status
            'upload_status': video_info_json.get('status', {}).get('uploadStatus'),
            'privacy_status': video_info_json.get('status', {}).get('privacyStatus'),
            'license': video_info_json.get('status', {}).get('license'),
            'embeddable': video_info_json.get('status', {}).get('embeddable', False),
            'public_stats_viewable': video_info_json.get('status', {}).get('publicStatsViewable', False),

            # Player
            'embed_html': video_info_json.get('player', {}).get('embedHtml'),

            # Дополнительные поля, если доступны
            'topics': video_info_json.get('topicDetails', {}).get('topicCategories', []),
            'recording_location': (
                video_info_json.get('recordingDetails', {}).get('locationDescription') or
                (
                    f"{video_info_json.get('recordingDetails', {}).get('location', {}).get('latitude', '')}, "
                    f"{video_info_json.get('recordingDetails', {}).get('location', {}).get('longitude', '')}"
                ) if video_info_json.get('recordingDetails', {}).get('location') else None
            ),
            'recording_date': video_info_json.get('recordingDetails', {}).get('recordingDate'),
        }

        # Дополнительно извлекаем параметры для прямых трансляций, если они есть
        if 'liveStreamingDetails' in video_info_json:
            live_details = video_info_json['liveStreamingDetails']
            result.update({
                'actual_start_time': live_details.get('actualStartTime'),
                'actual_end_time': live_details.get('actualEndTime'),
                'scheduled_start_time': live_details.get('scheduledStartTime'),
                'scheduled_end_time': live_details.get('scheduledEndTime'),
                'concurrent_viewers': live_details.get('concurrentViewers'),
                'active_live_chat': live_details.get('activeLiveChatId')
            })

        return result

    except HttpError as e:
        print(f"Ошибка при получении информации о видео: {e}")
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при получении информации о видео: {e}")
        raise

def get_video_comments(video_id, max_results=100, order='time', text_format='plainText'):
    """
    Получает комментарии к видео.

    Args:
        video_id (str): ID видео на YouTube.
        max_results (int, optional): Максимальное количество комментариев. По умолчанию 100.
        order (str, optional): Порядок сортировки комментариев ('time' или 'relevance'). По умолчанию 'time'.
        text_format (str, optional): Формат текста комментариев ('plainText' или 'html'). По умолчанию 'plainText'.

    Returns:
        list: Список словарей с информацией о комментариях, включая ответы.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()

        comments = []
        next_page_token = None

        while True:
            # Параметры запроса
            request_params = {
                'part': 'snippet,replies',
                'videoId': video_id,
                'maxResults': min(100, max_results - len(comments)),  # API ограничивает до 100 за запрос
                'order': order,
                'textFormat': text_format
            }

            if next_page_token:
                request_params['pageToken'] = next_page_token

            # Выполняем запрос на получение комментариев
            comment_threads_response = youtube.commentThreads().list(**request_params).execute()

            # Обрабатываем полученные комментарии
            for item in comment_threads_response['items']:
                comment = item['snippet']['topLevelComment']['snippet']

                comment_info = {
                    'id': item['id'],
                    'comment_id': item['snippet']['topLevelComment']['id'],
                    'text': comment['textDisplay'],
                    'author_display_name': comment['authorDisplayName'],
                    'author_profile_image_url': comment['authorProfileImageUrl'],
                    'author_channel_url': comment['authorChannelUrl'],
                    'author_channel_id': comment.get('authorChannelId', {}).get('value'),
                    'like_count': comment['likeCount'],
                    'published_at': comment['publishedAt'],
                    'updated_at': comment['updatedAt'],
                    'reply_count': item['snippet']['totalReplyCount'],
                    'replies': []
                }

                # Обработка ответов на комментарий, если они есть
                if 'replies' in item and item['snippet']['totalReplyCount'] > 0:
                    for reply_item in item['replies']['comments']:
                        reply = reply_item['snippet']

                        reply_info = {
                            'id': reply_item['id'],
                            'text': reply['textDisplay'],
                            'author_display_name': reply['authorDisplayName'],
                            'author_profile_image_url': reply['authorProfileImageUrl'],
                            'author_channel_url': reply['authorChannelUrl'],
                            'author_channel_id': reply.get('authorChannelId', {}).get('value'),
                            'like_count': reply['likeCount'],
                            'published_at': reply['publishedAt'],
                            'updated_at': reply['updatedAt'],
                            'parent_id': comment_info['comment_id']
                        }

                        comment_info['replies'].append(reply_info)

                comments.append(comment_info)

            # Проверяем, нужно ли загружать следующую страницу результатов
            next_page_token = comment_threads_response.get('nextPageToken')

            # Прекращаем загрузку, если достигли максимального количества комментариев или больше нет страниц
            if not next_page_token or len(comments) >= max_results:
                break

        return comments[:max_results]

    except HttpError as e:
        # Если комментарии отключены, обработаем ошибку
        if "commentsDisabled" in str(e):
            print(f"Комментарии для видео {video_id} отключены.")
            return []
        print(f"Ошибка при получении комментариев видео: {e}")
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при получении комментариев видео: {e}")
        raise

def get_video_categories(region_code="RU"):
    """
    Получает список категорий видео.

    Args:
        region_code (str, optional): Код региона. По умолчанию "RU".

    Returns:
        list: Список словарей с информацией о категориях видео.

    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()

        # Получаем категории видео
        categories_response = youtube.videoCategories().list(
            part='snippet',
            regionCode=region_code
        ).execute()

        categories = []

        for item in categories_response['items']:
            category_info = {
                'id': item['id'],
                'title': item['snippet']['title'],
                'assignable': item['snippet']['assignable'],
                'channel_id': item['snippet'].get('channelId')
            }

            categories.append(category_info)

        return categories

    except HttpError as e:
        print(f"Ошибка при получении категорий видео: {e}")
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при получении категорий видео: {e}")
        raise

def get_channel_videos(channel_id, max_results=10):
    """
    Получает список видео с указанного канала YouTube.
    Сохраняет каждое видео в базу данных.

    Args:
        channel_id (str): ID канала YouTube.
        max_results (int, optional): Максимальное количество видео для получения. По умолчанию 10.

    Returns:
        list: Список словарей с информацией о видео.
    """
    try:
        youtube = get_youtube_service()

        # 1. Получаем ID плейлиста загрузок канала
        # Мы можем использовать существующую get_channel_details, но она возвращает много данных.
        # Для оптимизации, если нужен только uploads_playlist_id, можно сделать более легковесный запрос.
        # Однако, для простоты и повторного использования, воспользуемся get_channel_details.
        # Либо можно напрямую запросить contentDetails канала.
        channel_details_response = youtube.channels().list(
            part='contentDetails',
            id=channel_id
        ).execute()

        if not channel_details_response.get('items'):
            print(f"Канал с ID {channel_id} не найден.")
            return []

        uploads_playlist_id = channel_details_response['items'][0]['contentDetails'].get('relatedPlaylists', {}).get('uploads')

        if not uploads_playlist_id:
            print(f"Не удалось найти плейлист загрузок для канала {channel_id}.")
            return []

        # 2. Получаем элементы плейлиста (ID видео)
        playlist_items = []
        next_page_token = None
        while True:
            playlist_request = youtube.playlistItems().list(
                part='snippet', # Нам нужен только videoId из snippet на этом этапе
                playlistId=uploads_playlist_id,
                maxResults=min(max_results - len(playlist_items), 50), # API limit is 50 per page
                pageToken=next_page_token
            )
            playlist_response = playlist_request.execute()

            for item in playlist_response.get('items', []):
                if 'videoId' in item['snippet']['resourceId']:
                    playlist_items.append(item['snippet']['resourceId']['videoId'])

            next_page_token = playlist_response.get('nextPageToken')
            if not next_page_token or len(playlist_items) >= max_results:
                break

        video_ids_to_fetch = playlist_items[:max_results]

        if not video_ids_to_fetch:
            return []

        # 3. Получаем полную информацию для каждого видео и сохраняем в БД
        videos_details_list = []
        # YouTube API позволяет запрашивать до 50 видео ID за раз
        for i in range(0, len(video_ids_to_fetch), 50):
            chunk_video_ids = video_ids_to_fetch[i:i+50]
            videos_response = youtube.videos().list(
                part='snippet,contentDetails,statistics,status,player,topicDetails,recordingDetails',
                id=','.join(chunk_video_ids)
            ).execute()

            for video_item_json in videos_response.get('items', []):
                try:
                    save_video_data_to_db(video_item_json) # Сохраняем полный JSON
                except Exception as e_save:
                    print(f"Ошибка при попытке сохранить видео {video_item_json.get('id', 'UNKNOWN_ID')} из get_channel_videos: {e_save}")

                # Формируем словарь для возврата, аналогично search_videos
                processed_video_info = {
                    'id': video_item_json['id'],
                    'title': video_item_json['snippet']['title'],
                    'description': video_item_json['snippet']['description'],
                    'thumbnails': video_item_json['snippet']['thumbnails'],
                    'published_at': video_item_json['snippet']['publishedAt'],
                    'channel_id': video_item_json['snippet']['channelId'],
                    'channel_title': video_item_json['snippet']['channelTitle'],
                    'url': f'https://www.youtube.com/watch?v={video_item_json["id"]}'
                }
                if 'contentDetails' in video_item_json:
                    processed_video_info['duration'] = video_item_json['contentDetails']['duration']
                if 'statistics' in video_item_json:
                    processed_video_info['statistics'] = video_item_json['statistics']

                videos_details_list.append(processed_video_info)

        return videos_details_list

    except HttpError as e:
        print(f"Ошибка YouTube API при получении видео канала {channel_id}: {e}")
        # В зависимости от типа ошибки, можно вернуть [] или пробросить исключение
        if e.resp.status == 404:
            print(f"Канал {channel_id} не найден или плейлист загрузок отсутствует (404).")
            return []
        raise # Пробрасываем другие ошибки API
    except Exception as e:
        print(f"Непредвиденная ошибка при получении видео канала {channel_id}: {e}")
        raise # Пробрасываем непредвиденные ошибки

def create_youtube_channels_table(conn):
    """Создает таблицу youtube_channels, если она еще не существует."""
    sql = """
    CREATE TABLE IF NOT EXISTS youtube_channels (
        channel_id VARCHAR(255) PRIMARY KEY,
        title VARCHAR(255),
        description TEXT,
        custom_url VARCHAR(255) UNIQUE,  -- Может быть NULL, если не установлено
        published_at TIMESTAMP,
        country VARCHAR(10),            -- Код страны, например, "US"
        thumbnail_default_url VARCHAR(255),
        thumbnail_medium_url VARCHAR(255),
        thumbnail_high_url VARCHAR(255),
        view_count BIGINT,              -- Используем BIGINT для больших чисел
        subscriber_count BIGINT,       -- Используем BIGINT для больших чисел (может быть скрыто)
        hidden_subscriber_count BOOLEAN,
        video_count INTEGER,
        uploads_playlist_id VARCHAR(255),
        topic_ids TEXT[],             -- Массив ID тем, связанных с каналом
        keywords TEXT,                -- Ключевые слова канала, если есть (из brandingSettings)
        banner_external_url VARCHAR(255), -- URL баннера канала
        tracking_analytics_account_id VARCHAR(255), -- ID аккаунта Google Analytics
        unsubscribed_trailer VARCHAR(50), -- ID видео-трейлера для не подписанных
        last_retrieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    try:
        with conn.cursor() as cur:
            cur.execute(sql)
            conn.commit()
            print("Таблица youtube_channels успешно создана или уже существует.")
    except psycopg2.Error as e:
        print(f"Ошибка при создании таблицы youtube_channels: {e}")
        if conn:
            conn.rollback()
        raise # Передаем исключение выше

def save_channel_details_to_db(channel_data_json):
    """
    Сохраняет или обновляет данные о канале в таблице youtube_channels.
    channel_data_json - это словарь, представляющий JSON объекта канала из API.
    """
    if not channel_data_json or 'id' not in channel_data_json:
        print("Ошибка: неверные данные канала для сохранения.")
        return

    channel_id = channel_data_json['id']
    snippet = channel_data_json.get('snippet', {})
    statistics = channel_data_json.get('statistics', {})
    content_details = channel_data_json.get('contentDetails', {})
    topic_details = channel_data_json.get('topicDetails', {})
    branding_settings = channel_data_json.get('brandingSettings', {}).get('channel', {})
    image_settings = channel_data_json.get('brandingSettings', {}).get('image', {})

    # Извлечение данных
    title = snippet.get('title')
    description = snippet.get('description')
    custom_url = snippet.get('customUrl')
    published_at = snippet.get('publishedAt')
    country = snippet.get('country')

    thumbnails = snippet.get('thumbnails', {})
    thumbnail_default_url = thumbnails.get('default', {}).get('url')
    thumbnail_medium_url = thumbnails.get('medium', {}).get('url')
    thumbnail_high_url = thumbnails.get('high', {}).get('url')

    view_count = int(statistics.get('viewCount', 0)) if statistics.get('viewCount') else 0
    # subscriberCount может быть скрыт, API вернет hiddenSubscriberCount = true
    subscriber_count_str = statistics.get('subscriberCount')
    subscriber_count = int(subscriber_count_str) if subscriber_count_str else 0
    hidden_subscriber_count = statistics.get('hiddenSubscriberCount', False)
    video_count = int(statistics.get('videoCount', 0)) if statistics.get('videoCount') else 0

    uploads_playlist_id = content_details.get('relatedPlaylists', {}).get('uploads')
    topic_ids = topic_details.get('topicIds', [])

    keywords = branding_settings.get('keywords') # Строка ключевых слов через пробел
    unsubscribed_trailer = branding_settings.get('unsubscribedTrailer')
    banner_external_url = image_settings.get('bannerExternalUrl')
    tracking_analytics_account_id = channel_data_json.get('analytics',{}).get('trackingAnalyticsAccountId') # Это обычно в auditDetails, но его нет в стандартном parts

    sql_insert_channel = """
    INSERT INTO youtube_channels (
        channel_id, title, description, custom_url, published_at, country,
        thumbnail_default_url, thumbnail_medium_url, thumbnail_high_url,
        view_count, subscriber_count, hidden_subscriber_count, video_count,
        uploads_playlist_id, topic_ids, keywords, banner_external_url,
        tracking_analytics_account_id, unsubscribed_trailer,
        last_retrieved_at, created_at, updated_at
    )
    VALUES (
        %s, %s, %s, %s, %s, %s,
        %s, %s, %s,
        %s, %s, %s, %s,
        %s, %s, %s, %s,
        %s, %s,
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
    )
    ON CONFLICT (channel_id) DO UPDATE SET
        title = EXCLUDED.title,
        description = EXCLUDED.description,
        custom_url = EXCLUDED.custom_url,
        published_at = EXCLUDED.published_at,
        country = EXCLUDED.country,
        thumbnail_default_url = EXCLUDED.thumbnail_default_url,
        thumbnail_medium_url = EXCLUDED.thumbnail_medium_url,
        thumbnail_high_url = EXCLUDED.thumbnail_high_url,
        view_count = EXCLUDED.view_count,
        subscriber_count = EXCLUDED.subscriber_count,
        hidden_subscriber_count = EXCLUDED.hidden_subscriber_count,
        video_count = EXCLUDED.video_count,
        uploads_playlist_id = EXCLUDED.uploads_playlist_id,
        topic_ids = EXCLUDED.topic_ids,
        keywords = EXCLUDED.keywords,
        banner_external_url = EXCLUDED.banner_external_url,
        tracking_analytics_account_id = EXCLUDED.tracking_analytics_account_id,
        unsubscribed_trailer = EXCLUDED.unsubscribed_trailer,
        last_retrieved_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP;
    """

    conn = None
    try:
        conn = get_db_connection()
        if conn:
            # Убедимся, что таблица существует перед вставкой
            create_youtube_channels_table(conn) # Вызов функции создания таблицы

            with conn.cursor() as cur:
                cur.execute(sql_insert_channel, (
                    channel_id, title, description, custom_url, published_at, country,
                    thumbnail_default_url, thumbnail_medium_url, thumbnail_high_url,
                    view_count, subscriber_count, hidden_subscriber_count, video_count,
                    uploads_playlist_id, topic_ids, keywords, banner_external_url,
                    tracking_analytics_account_id, unsubscribed_trailer
                ))
                conn.commit()
                print(f"Канал {channel_id} ({title}) успешно сохранен/обновлен в таблице youtube_channels.")
    except psycopg2.Error as e:
        print(f"Ошибка при сохранении канала {channel_id} в БД: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"Непредвиденная ошибка при сохранении канала {channel_id} в БД: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

print("Добавьте YOUTUBE_API_KEY и параметры БД (DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASS) в файл .env")

if __name__ == '__main__':
    # Пример использования
    if YOUTUBE_API_KEY:
        try:
            print("Тестирование модуля youtube_api.py...")

            print("\n1. Настройка сбора данных о каналах:")
            fetch_channels_choice = input("Собирать и сохранять информацию о новых каналах из результатов поиска? (y/n, по умолчанию y): ")
            fetch_and_save_new_channels_flag = fetch_channels_choice.lower() != 'n'
            if fetch_and_save_new_channels_flag:
                 print("Информация о новых каналах будет собираться и сохраняться.")
            else:
                 print("Информация о каналах собираться НЕ будет.")

            print("\n2. Поиск видео:")
            search_query = input("Введите поисковый запрос: ")

            print("\nДополнительные параметры поиска (нажмите Enter для значений по умолчанию):")
            max_results_input = input("Количество результатов (по умолчанию 5): ")
            max_results = int(max_results_input) if max_results_input.strip() else 5

            print("\nВыберите порядок сортировки:")
            print("1. По релевантности (relevance) - видео за последние 4 месяца")
            print("2. По дате публикации (date) - все видео")
            print("3. По количеству просмотров (viewCount) - все видео")
            print("4. По рейтингу (rating) - все видео")
            print("5. По названию (title) - все видео")
            order_choice = input("Ваш выбор (по умолчанию 1): ")
            order_map = {"1": "relevance", "2": "date", "3": "viewCount", "4": "rating", "5": "title"}
            order = order_map.get(order_choice.strip(), "relevance")

            region_code = "US"
            relevance_language = "en"
            published_after = None
            published_before = None
            if order == "relevance":
                four_months_ago = datetime.now() - timedelta(days=4*30)
                published_after_default = four_months_ago.strftime('%Y-%m-%dT%H:%M:%SZ')
                print(f"(По умолчанию для релевантности поиск за последние 4 месяца, начиная с {four_months_ago.strftime('%Y-%m-%d')})")
            else:
                published_after_default = None
            published_after_input = input(f"Опубликовано после (YYYY-MM-DD, по умолчанию - {four_months_ago.strftime('%Y-%m-%d') if published_after_default else 'нет'}): ")
            if published_after_input.strip():
                published_after = f"{published_after_input.strip()}T00:00:00Z"
            elif published_after_default and order == "relevance":
                 published_after = published_after_default
            published_before_input = input("Опубликовано до (YYYY-MM-DD, по умолчанию - нет): ")
            if published_before_input.strip():
                published_before = f"{published_before_input.strip()}T23:59:59Z"

            print(f"\nВыполняю поиск: '{search_query}' с параметрами:")
            print(f"- Количество результатов: {max_results}")
            print(f"- Сортировка: {order}")
            print(f"- Регион: {region_code}")
            print(f"- Язык: {relevance_language}")
            if published_after: print(f"- Опубликовано после: {published_after[:10]}")
            if published_before: print(f"- Опубликовано до: {published_before[:10]}")

            results = search_videos(
                query=search_query,
                max_results=max_results,
                region_code=region_code,
                relevance_language=relevance_language,
                order=order,
                published_after=published_after,
                published_before=published_before,
                fetch_and_save_new_channels=fetch_and_save_new_channels_flag
            )

            if not results:
                print("Видео не найдены по вашему запросу.")
            else:
                for i, video in enumerate(results, 1):
                    print(f"\n{i}. {video['title']}")
                    print(f"   URL: {video['url']}")
                    print(f"   Канал: {video['channel_title']}")
                    print(f"   Опубликовано: {video['published_at']}")
                    if 'statistics' in video:
                        views = video['statistics'].get('viewCount', 'N/A')
                        likes = video['statistics'].get('likeCount', 'N/A')
                        print(f"   Просмотры: {views}, Лайки: {likes}")

        except ValueError as ve:
            print(f"Ошибка конфигурации: {ve}")
        except HttpError as he:
            print(f"Ошибка API: {he}")
        except Exception as e:
            print(f"Произошла ошибка: {e}")
    else:
        print("\nТестирование не может быть выполнено, так как YOUTUBE_API_KEY не установлен.")
