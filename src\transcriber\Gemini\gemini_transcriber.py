import os
import argparse
import google.generativeai as genai
from rich.console import Console
from rich.panel import Panel
from dotenv import load_dotenv
import datetime # Добавляем импорт datetime
import subprocess # Добавлен импорт subprocess
import imageio_ffmpeg # Добавлен импорт imageio_ffmpeg

def transcribe_audio(api_key, audio_file_path, with_timestamps=False, 
                     use_prompt=False, prompt_file=None, language=None):
    """
    Транскрибация аудиофайла с использованием Google Gemini 2.0 Flash.
    
    Параметры:
    - api_key: Ключ API для Gemini
    - audio_file_path: Путь к аудиофайлу
    - with_timestamps: Запрашивать таймкоды в транскрипции
    - use_prompt: Использовать расширенный промпт
    - prompt_file: Путь к файлу с промптом (если None, ищет файлы prompt.txt или transcript_prompt.txt)
    - language: Язык для транскрибации (например, 'ru' для русского, 'en' для английского)
    
    Возвращает:
    - tuple: (transcript, token_info) или (None, None) в случае ошибки
    """
    console = Console()
    
    try:
        # Конфигурация API ключа
        genai.configure(api_key=api_key)
        
        console.print("[bold blue]Конфигурация API ключа успешна[/bold blue]")
        console.print(f"[bold green]Обрабатываем файл:[/bold green] {audio_file_path}")
        
        # Проверка существования файла
        if not os.path.exists(audio_file_path):
            console.print(f"[bold red]Ошибка:[/bold red] Файл не найден: {audio_file_path}")
            return None, None
        
        # Определение типа файла на основе расширения
        file_extension = os.path.splitext(audio_file_path)[1].lower()
        mime_types = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.aiff': 'audio/aiff',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.flac': 'audio/flac'
        }
        
        mime_type = mime_types.get(file_extension)
        if not mime_type:
            console.print(f"[bold yellow]Предупреждение:[/bold yellow] Неизвестный формат файла. Попытка определить автоматически.")
            mime_type = 'audio/mpeg'  # Предполагаем MP3 по умолчанию
        
        # Определение размера файла
        file_size = os.path.getsize(audio_file_path) / (1024 * 1024)  # В МБ
        console.print(f"Размер файла: {file_size:.2f} МБ")
        
        # --- Новая логика формирования промпта ---
        prompt_elements = []
        language_name_for_display = language # e.g., 'ru' or 'английский'

        # Определяем полное имя языка для промпта и отображения
        if language:
            language_mapping = {
                'ru': 'русский', 'en': 'английский', 'fr': 'французский',
                'de': 'немецкий', 'es': 'испанский', 'it': 'итальянский',
                'pt': 'португальский', 'zh': 'китайский', 'ja': 'японский'
            }
            language_name_for_display = language_mapping.get(language.lower(), language)
            console.print(f"[bold blue]Язык результата:[/bold blue] {language_name_for_display}")

        # Базовая инструкция, будет изменена если пользователь передаст свой файл промпта
        # Сразу указываем язык в основной инструкции
        current_main_instruction = f"Транскрибируй это аудио на {language_name_for_display} язык."

        if use_prompt:
            chosen_prompt_file_path = prompt_file # Если был передан через --prompt-file
            if not chosen_prompt_file_path: # Если --prompt-file не указан, но --prompt есть, ищем стандартные имена
                default_prompt_filenames = ["prompt.txt", "transcript_prompt.txt"]
                for pf_name in default_prompt_filenames:
                    if os.path.exists(pf_name):
                        chosen_prompt_file_path = pf_name
                        break
            
            if chosen_prompt_file_path and os.path.exists(chosen_prompt_file_path):
                console.print(f"[bold blue]Используется основной промпт из файла:[/bold blue] {chosen_prompt_file_path}")
                with open(chosen_prompt_file_path, 'r', encoding='utf-8') as f:
                    current_main_instruction = f.read().strip() # Промпт пользователя заменяет базовый
                
                # Добавляем усиливающую инструкцию о языке, если пользовательский промпт не содержит явного указания
                # (простая проверка на наличие названия языка в промпте пользователя)
                if language and language_name_for_display.lower() not in current_main_instruction.lower():
                    reinforcing_language_instruction = f"Важно: весь текст транскрипции должен быть предоставлен исключительно на {language_name_for_display} языке."
                    prompt_elements.append(reinforcing_language_instruction)
            
            elif prompt_file: # Если --prompt-file был указан, но файл не найден
                 console.print(f"[bold yellow]Предупреждение:[/bold yellow] Указанный файл промпта '{prompt_file}' не найден. Используется стандартный промпт с указанием языка.")
            elif use_prompt: # Если просто --prompt, но стандартные файлы не найдены
                 console.print("[bold yellow]Предупреждение:[/bold yellow] Флаг --prompt указан, но файлы 'prompt.txt' или 'transcript_prompt.txt' не найдены. Используется стандартный промпт с указанием языка.")

        prompt_elements.insert(0, current_main_instruction) # Основная инструкция (базовая или из файла) всегда первая

        if with_timestamps:
            timestamp_instruction = "Добавь таймкоды формата [MM:SS] каждые 30 секунд."
            # Проверяем, нет ли уже запроса на таймкоды в элементах промпта
            if not any(("таймкод" in el.lower() or "timestamp" in el.lower()) for el in prompt_elements):
                prompt_elements.append(timestamp_instruction)
        
        final_prompt = "\n".join(prompt_elements)
        console.print(Panel(final_prompt, title="[bold blue]Финальный промпт для Gemini[/bold blue]", border_style="blue", expand=False))
        # --- Конец новой логики формирования промпта ---
        
        # Инициализация модели
        model = genai.GenerativeModel('gemini-2.0-flash')

        # Выбор метода загрузки в зависимости от размера файла
        if file_size > 20: # Gemini API limit for inline content is often around this
            console.print("[bold blue]Размер файла больше ~20 МБ, используем метод загрузки файла на сервер Gemini...[/bold blue]")
            audio_file_for_api = genai.upload_file(path=audio_file_path) # This call might take time
            console.print(f"Файл '{audio_file_for_api.display_name}' загружен на сервер Gemini.")
            
            # Подсчет входных токенов
            try:
                count_response = model.count_tokens([final_prompt, audio_file_for_api])
                input_token_count = count_response.total_tokens
                console.print(f"[bold yellow]Примерное количество входных токенов:[/bold yellow] {input_token_count}")
            except Exception as count_e:
                console.print(f"[bold red]Ошибка при подсчете входных токенов:[/bold red] {str(count_e)}")
                input_token_count = "Не удалось подсчитать"

            console.print("[bold blue]Начинаем транскрибацию (может занять некоторое время)...[/bold blue]")
            response = model.generate_content([final_prompt, audio_file_for_api])
        else:
            console.print("[bold blue]Файл меньше ~20 МБ, используем inline метод передачи данных...[/bold blue]")
            with open(audio_file_path, 'rb') as f:
                audio_bytes = f.read()
            
            audio_part_for_api = {"mime_type": mime_type, "data": audio_bytes}
            
            # Подсчет входных токенов
            try:
                count_response = model.count_tokens([final_prompt, audio_part_for_api])
                input_token_count = count_response.total_tokens
                console.print(f"[bold yellow]Примерное количество входных токенов:[/bold yellow] {input_token_count}")
            except Exception as count_e:
                console.print(f"[bold red]Ошибка при подсчете входных токенов:[/bold red] {str(count_e)}")
                input_token_count = "Не удалось подсчитать"

            console.print("[bold blue]Начинаем транскрибацию...[/bold blue]")
            response = model.generate_content([final_prompt, audio_part_for_api])
        
        # Получение результата
        transcript = response.text
        
        console.print("[bold green]Транскрибация завершена![/bold green]")

        # Получение информации об использовании токенов из ответа
        try:
            output_token_count = response.usage_metadata.candidates_token_count
            total_token_count = response.usage_metadata.total_token_count
            console.print(f"[bold yellow]Количество выходных токенов:[/bold yellow] {output_token_count}")
            # Общее количество может отличаться от суммы входа+выхода из-за внутренних процессов
            console.print(f"[bold yellow]Общее количество токенов (по данным API):[/bold yellow] {total_token_count}") 
        except AttributeError:
            console.print("[bold yellow]Информация об использовании токенов недоступна в ответе API.[/bold yellow]")
            output_token_count = "Недоступно"
            total_token_count = "Недоступно"
        
        # Вывод результата
        console.print(Panel(transcript[:1000] + "..." if len(transcript) > 1000 else transcript, 
                           title="Фрагмент транскрипции", border_style="green"))
        
        # Формирование строки с информацией о токенах
        token_info = f"--- Информация о токенах ---\n"
        token_info += f"Входные токены (оценка): {input_token_count}\n"
        token_info += f"Выходные токены: {output_token_count}\n"
        token_info += f"Общие токены (API): {total_token_count}\n"
        token_info += f"(Примечание: Оценка входных токенов может отличаться от фактического значения, рассчитанного API.)\n"
        token_info += f"---------------------------\n\n"

        # Возвращаем транскрипт и информацию о токенах
        return transcript, token_info
        
    except Exception as e:
        console.print(f"[bold red]Ошибка при транскрибации:[/bold red] {str(e)}")
        return None, None

def main():
    load_dotenv() # Загрузка переменных окружения из .env файла
    console = Console()

    # Настройка парсера аргументов
    parser = argparse.ArgumentParser(description='Транскрибация аудиофайлов с помощью Gemini 2.0 Flash.')
    parser.add_argument('target_input', help='Путь к аудиофайлу или ID видео для поиска в папке downloads.')
    parser.add_argument('-o', '--output-name', default='транскрипт', 
                        help='Базовое имя файла для сохранения результата (без расширения и даты). По умолчанию: транскрипт.')
    parser.add_argument('-t', '--timestamps', action='store_true', help='Добавить таймкоды в транскрипцию.')
    parser.add_argument('-p', '--prompt', action='store_true', help='Использовать расширенный промпт из файла (prompt.txt или transcript_prompt.txt).')
    parser.add_argument('-f', '--prompt-file', help='Указать конкретный файл с промптом.')
    parser.add_argument('-l', '--language', default='ru', help='Язык для транскрибации (например, ru, en). По умолчанию: ru.')
    
    args = parser.parse_args()

    # Получение API ключа только из окружения
    api_key = os.getenv("GEMINI_API_KEY")
    
    if not api_key:
        console.print("[bold red]Ошибка:[/bold red] Ключ API Gemini не найден. Установите переменную окружения GEMINI_API_KEY.")
        return

    audio_file_path = None
    target_input_val = args.target_input

    # Проверяем, является ли target_input_val путем к существующему файлу
    if os.path.isfile(target_input_val):
        audio_file_path = os.path.abspath(target_input_val)
        console.print(f"[bold blue]Используется прямой путь к файлу:[/bold blue] {audio_file_path}")
    else:
        # Предполагаем, что target_input_val - это video_id
        video_id = target_input_val
        console.print(f"[bold blue]Поиск аудиофайла для ID видео:[/bold blue] {video_id}")
        
        script_dir = os.path.dirname(os.path.abspath(__file__))
        # Путь к папке downloads относительно корня проекта
        # Workspace: /e:/Coding/ai/aiAgents/youtube_downloader
        # Script: /e:/Coding/ai/aiAgents/youtube_downloader/src/transcriber/Gemini/gemini_transcriber.py
        # Downloads: /e:/Coding/ai/aiAgents/youtube_downloader/downloads
        project_root = os.path.abspath(os.path.join(script_dir, "..", "..", ".."))
        downloads_base_path = os.path.join(project_root, "downloads")

        if not os.path.isdir(downloads_base_path):
            console.print(f"[bold red]Ошибка:[/bold red] Директория downloads не найдена по пути: {downloads_base_path}")
            return

        console.print(f"[bold blue]Поиск в директории:[/bold blue] {downloads_base_path}")
        
        audio_file_path = None
        found_audio_file_in_downloads = False
        # Расширен список расширений, добавлен .webm
        audio_extensions = ['.mp3', '.wav', '.aiff', '.aac', '.ogg', '.flac', '.m4a', '.webm'] 

        possible_video_folders = []
        try:
            for item in os.listdir(downloads_base_path):
                item_path = os.path.join(downloads_base_path, item)
                if os.path.isdir(item_path) and item.startswith(video_id):
                    possible_video_folders.append(item_path)
        except OSError as e:
            console.print(f"[bold red]Ошибка при доступе к директории downloads {downloads_base_path}:[/bold red] {e}")
            return
        
        if not possible_video_folders:
            console.print(f"[bold red]Ошибка:[/bold red] Не найдено директорий в '{downloads_base_path}', имя которых начинается с ID '{video_id}'.")
            return

        for video_folder_path in possible_video_folders:
            console.print(f"[bold blue]Проверка папки:[/bold blue] {video_folder_path}")
            try:
                # 1. Искать аудиофайлы непосредственно в video_folder_path
                for file_name in os.listdir(video_folder_path):
                    potential_file_path = os.path.join(video_folder_path, file_name)
                    if os.path.isfile(potential_file_path) and any(file_name.lower().endswith(ext) for ext in audio_extensions):
                        audio_file_path = potential_file_path
                        console.print(f"[bold green]Найден аудиофайл (напрямую):[/bold green] {audio_file_path}")
                        found_audio_file_in_downloads = True
                        break 
                
                if found_audio_file_in_downloads:
                    break # Выходим из цикла по possible_video_folders

                # 2. Если не найдено напрямую, искать в подпапках (на один уровень глубже)
                console.print(f"[bold blue]Проверка подпапок в:[/bold blue] {video_folder_path}")
                for item_name in os.listdir(video_folder_path):
                    item_path = os.path.join(video_folder_path, item_name)
                    if os.path.isdir(item_path):
                        # console.print(f"  [bold_dim]Проверка подпапки:[/bold_dim] {item_path}") # Для отладки можно раскомментировать
                        for sub_file_name in os.listdir(item_path):
                            potential_sub_file_path = os.path.join(item_path, sub_file_name)
                            if os.path.isfile(potential_sub_file_path) and any(sub_file_name.lower().endswith(ext) for ext in audio_extensions):
                                audio_file_path = potential_sub_file_path
                                console.print(f"[bold green]Найден аудио/видео файл (в подпапке {item_name}):[/bold green] {audio_file_path}")
                                found_audio_file_in_downloads = True
                                break # Выходим из цикла по sub_file_name
                    if found_audio_file_in_downloads:
                        break # Выходим из цикла по item_name
                
                if found_audio_file_in_downloads:
                    break # Выходим из цикла по possible_video_folders

            except OSError as e:
                console.print(f"[bold red]Ошибка при доступе к папке {video_folder_path} или ее содержимому:[/bold red] {e}")
                continue 
        
        if not audio_file_path:
            searched_folders_str = "\n".join([f"  - {folder}" for folder in possible_video_folders])
            console.print(f"[bold red]Ошибка:[/bold red] Аудиофайл для ID видео '{video_id}' не найден.")
            console.print(f"[bold yellow]Проверены следующие директории (включая их прямые подпапки), но в них не найдено аудиофайлов с расширениями {audio_extensions}:[/bold yellow]")
            if possible_video_folders:
                 console.print(searched_folders_str)
            else:
                console.print(f"  (Не найдено папок, начинающихся с ID '{video_id}' в '{downloads_base_path}')")
            return

    # Конвертация WebM в MP3, если необходимо
    if audio_file_path and audio_file_path.lower().endswith('.webm'):
        console.print(f"[bold blue]Обнаружен файл WebM:[/bold blue] {audio_file_path}")
        mp3_file_path = os.path.splitext(audio_file_path)[0] + ".mp3"
        console.print(f"[bold blue]Попытка конвертации в MP3:[/bold blue] {mp3_file_path}")
        
        try:
            ffmpeg_exe = imageio_ffmpeg.get_ffmpeg_exe()
            console.print(f"[bold_dim]Используется ffmpeg из {ffmpeg_exe}[/bold_dim]")
            cmd = [
                ffmpeg_exe,
                '-i', audio_file_path,
                '-vn',         # Отключить видео
                '-ab', '192k',  # Аудио битрейт
                '-ar', '44100', # Частота дискретизации
                '-y',          # Перезаписать без запроса
                mp3_file_path
            ]
            process = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if process.returncode == 0:
                console.print(f"[bold green]Конвертация в MP3 успешно завершена:[/bold green] {mp3_file_path}")
                audio_file_path = mp3_file_path # Используем сконвертированный файл для транскрибации
            else:
                console.print(f"[bold red]Ошибка конвертации WebM в MP3.[/bold red]")
                console.print(f"FFmpeg stderr: {process.stderr}")
                # Решаем, что делать дальше - можно прервать или попытаться использовать оригинал (хотя Gemini его не примет)
                console.print(f"[bold yellow]Транскрибация будет пропущена для файла {os.path.basename(audio_file_path)} из-за ошибки конвертации.[/bold yellow]")
                return # Прерываем выполнение для этого файла
        except FileNotFoundError:
            console.print(f"[bold red]Ошибка:[/bold red] Исполняемый файл ffmpeg не найден. Убедитесь, что imageio-ffmpeg установлен корректно.")
            console.print(f"[bold yellow]Транскрибация будет пропущена для файла {os.path.basename(audio_file_path)}.[/bold yellow]")
            return
        except Exception as e:
            console.print(f"[bold red]Неожиданная ошибка при конвертации WebM в MP3:[/bold red] {e}")
            console.print(f"[bold yellow]Транскрибация будет пропущена для файла {os.path.basename(audio_file_path)}.[/bold yellow]")
            return

    if not audio_file_path:
        console.print(f"[bold red]Ошибка:[/bold red] Не удалось определить путь к аудиофайлу из '{target_input_val}'.")
        return

    # Проверка, что файл все еще существует (особенно после потенциальной конвертации)
    if not os.path.exists(audio_file_path):
        console.print(f"[bold red]Ошибка:[/bold red] Финальный аудиофайл не найден по пути: {audio_file_path}")
        return

    # Получение текущей даты и времени и форматирование
    now = datetime.datetime.now()
    timestamp_str = now.strftime("%y-%m-%d_%H-%M")

    # Формирование имени файла транскрипции
    user_specified_output_name = args.output_name
    final_base_output_name = ""

    if user_specified_output_name != 'транскрипт':
        final_base_output_name = user_specified_output_name
    else:
        if not os.path.isfile(target_input_val): # Вход был ID видео
            final_base_output_name = target_input_val
        else: # Вход был прямой путь к файлу
            final_base_output_name = os.path.splitext(os.path.basename(audio_file_path))[0]
    
    output_filename = f"{timestamp_str}_{final_base_output_name}.txt"

    # Определение пути сохранения: в той же папке, где лежит аудиофайл
    if not audio_file_path or not os.path.dirname(audio_file_path):
        console.print(f"[bold red]Ошибка: Не удалось определить директорию исходного аудиофайла для сохранения транскрипции (путь: {audio_file_path}).[/bold red]")
        return
        
    output_directory_for_transcript = os.path.dirname(audio_file_path)
    output_file_path = os.path.join(output_directory_for_transcript, output_filename)
    
    console.print(f"[bold blue]Транскрипция будет сохранена в:[/bold blue] {output_file_path}")

    # Вызов функции транскрибации
    transcript, token_info = transcribe_audio(
        api_key=api_key,
        audio_file_path=audio_file_path, # Используем найденный или указанный путь
        with_timestamps=args.timestamps,
        use_prompt=args.prompt,
        prompt_file=args.prompt_file,
        language=args.language
    )

    # Сохранение результата, если транскрибация прошла успешно
    if transcript is not None and token_info is not None:
        try:
            with open(output_file_path, 'w', encoding='utf-8') as f:
                f.write(token_info)
                f.write(transcript)
            console.print(f"[bold green]Результат сохранен в файл:[/bold green] {output_file_path}")
        except IOError as e:
            console.print(f"[bold red]Ошибка при сохранении файла:[/bold red] {str(e)}")

if __name__ == "__main__":
    main() 