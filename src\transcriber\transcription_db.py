# src/transcriber/transcription_db.py
from psycopg2 import sql, extras as pg_extras # Используем extras для <PERSON>son
from datetime import datetime
from src.db_utils import get_db_connection # Общая функция соединения

# Эта функция будет вызываться из TranscriptionService или другого места
def initialize_transcripts_table():
    """
    Инициализирует базу данных: создает таблицу 'transcripts', если она не существует.
    """
    conn = None
    cur = None
    try:
        conn = get_db_connection()
        if not conn:
            print("TransDB: Не удалось подключиться к БД для инициализации таблицы transcripts.")
            return False
        cur = conn.cursor()

        create_table_query = """
        CREATE TABLE IF NOT EXISTS transcripts (
            id SERIAL PRIMARY KEY,
            video_id VARCHAR(255) UNIQUE NOT NULL,
            title TEXT,
            original_url TEXT,
            upload_date TIMESTAMP,
            duration_seconds INTEGER,
            channel_name TEXT,
            channel_url TEXT,
            downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            audio_file_path TEXT,
            video_data_folder TEXT,
            transcription TEXT, -- Общий столбец для транскрипции, может быть от любого провайдера
            gemini_transcription TEXT NULL, -- Новый столбец для транскрипции от Gemini
            transcription_prompt TEXT,
            transcribed_at TIMESTAMP,
            metadata JSONB,
            processing_status VARCHAR(50) DEFAULT 'pending',
            error_message TEXT NULL
        );
        """
        cur.execute(create_table_query)
        conn.commit()
        print("TransDB: Таблица 'transcripts' успешно проверена/создана.")

        # Проверка и добавление столбца gemini_transcription, если он отсутствует
        alter_table_query = """
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1
                FROM information_schema.columns
                WHERE table_name = 'transcripts' AND column_name = 'gemini_transcription'
            ) THEN
                ALTER TABLE transcripts ADD COLUMN gemini_transcription TEXT NULL;
                RAISE NOTICE 'Столбец gemini_transcription добавлен в таблицу transcripts.';
            END IF;
        END $$;
        """
        cur.execute(alter_table_query)
        conn.commit()
        print("TransDB: Столбец 'gemini_transcription' успешно проверен/добавлен.")
        return True
    except Exception as e:
        print(f"TransDB: Ошибка при инициализации таблицы 'transcripts': {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def save_transcription_data(
    video_id: str,
    title: str,
    original_url: str,
    upload_date_str: str, 
    duration_seconds: int,
    channel_name: str,
    channel_url: str,
    audio_file_path: str,
    video_data_folder: str,
    transcription_text: str, # Это может быть основной текст или от другого провайдера
    gemini_transcription_text: str = None, # Текст от Gemini
    transcription_prompt_text: str = None,
    full_metadata: dict = None,
    status: str = 'transcribed'
) -> int | None:
    """
    Сохраняет данные о видео и его транскрипции в таблицу 'transcripts'.
    Возвращает ID созданной записи или None в случае ошибки.
    """
    conn = None
    cur = None
    record_id = None
    parsed_upload_date = None
    if upload_date_str:
        try:
            parsed_upload_date = datetime.strptime(upload_date_str, "%Y%m%d")
        except ValueError:
            print(f"TransDB: Предупреждение: Не удалось распознать дату загрузки '{upload_date_str}'. Будет сохранено как NULL.")
            parsed_upload_date = None

    try:
        conn = get_db_connection()
        if not conn:
            print(f"TransDB: Не удалось подключиться к БД для сохранения транскрипции видео ID '{video_id}'.")
            return None
        cur = conn.cursor()

        insert_query = sql.SQL("""
        INSERT INTO transcripts (
            video_id, title, original_url, upload_date, duration_seconds, 
            channel_name, channel_url, audio_file_path, video_data_folder, 
            transcription, gemini_transcription, transcription_prompt, transcribed_at, metadata, processing_status
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP, %s, %s)
        ON CONFLICT (video_id) DO UPDATE SET
            title = EXCLUDED.title,
            original_url = EXCLUDED.original_url,
            upload_date = EXCLUDED.upload_date,
            duration_seconds = EXCLUDED.duration_seconds,
            channel_name = EXCLUDED.channel_name,
            channel_url = EXCLUDED.channel_url,
            audio_file_path = EXCLUDED.audio_file_path,
            video_data_folder = EXCLUDED.video_data_folder,
            transcription = EXCLUDED.transcription, -- Обновляем общий столбец
            gemini_transcription = EXCLUDED.gemini_transcription, -- Обновляем столбец Gemini
            transcription_prompt = EXCLUDED.transcription_prompt,
            transcribed_at = CURRENT_TIMESTAMP,
            metadata = EXCLUDED.metadata,
            processing_status = EXCLUDED.processing_status,
            error_message = NULL
        RETURNING id;
        """)

        cur.execute(insert_query, (
            video_id, title, original_url, parsed_upload_date, duration_seconds,
            channel_name, channel_url, audio_file_path, video_data_folder,
            transcription_text, gemini_transcription_text, transcription_prompt_text, pg_extras.Json(full_metadata), status
        ))
        record_id = cur.fetchone()[0]
        conn.commit()
        print(f"TransDB: Данные для видео ID '{video_id}' успешно сохранены/обновлены в 'transcripts'. ID записи: {record_id}")
        return record_id
    except Exception as e:
        print(f"TransDB: Ошибка при сохранении данных в 'transcripts' для видео ID '{video_id}': {e}")
        if conn:
            conn.rollback()
        return None
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()

def update_transcription_status(video_id: str, status: str, error_message: str = None):
    """
    Обновляет статус обработки и сообщение об ошибке в таблице 'transcripts'.
    """
    conn = None
    cur = None
    try:
        conn = get_db_connection()
        if not conn:
            print(f"TransDB: Не удалось подключиться к БД для обновления статуса видео ID '{video_id}'.")
            return False
        cur = conn.cursor()
        query = sql.SQL("""
        UPDATE transcripts 
        SET processing_status = %s, error_message = %s, transcribed_at = CASE WHEN %s = 'transcribed' THEN CURRENT_TIMESTAMP ELSE transcribed_at END
        WHERE video_id = %s;
        """)
        cur.execute(query, (status, error_message, status, video_id))
        conn.commit()
        print(f"TransDB: Статус для видео ID '{video_id}' в 'transcripts' обновлен на '{status}'.")
        return True
    except Exception as e:
        print(f"TransDB: Ошибка при обновлении статуса в 'transcripts' для видео ID '{video_id}': {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cur:
            cur.close()
        if conn:
            conn.close()