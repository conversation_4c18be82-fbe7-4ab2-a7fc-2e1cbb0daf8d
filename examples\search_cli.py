# search_cli.py
import os
import sys
import argparse
from urllib.parse import urlparse, parse_qs
from dotenv import load_dotenv

# Добавляем корневую директорию проекта в sys.path для корректных импортов из src
PROJECT_ROOT_FOR_EXAMPLES = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT_FOR_EXAMPLES)

try:
    # Прямой импорт функций, чтобы избежать импорта других модулей
    from src.scrapers.youtube_api.youtube_api import search_videos, get_channel_videos, get_channel_details
    YOUTUBE_API_AVAILABLE = True
except ImportError as e:
    print(f"Ошибка: Не удалось импортировать функции из src.scrapers.youtube_api.youtube_api: {e}")
    print("Убедитесь, что файл существует и не содержит ошибок.")
    # Можно либо завершить выполнение, либо установить флаг, что API недоступно
    sys.exit(1)  
    
from googleapiclient.errors import HttpError

# Загружаем переменные окружения
load_dotenv()

def format_duration(duration_iso):
    """
    Преобразует длительность в формате ISO 8601 (PT1H2M3S) в читаемый формат (1:02:03).
    
    Args:
        duration_iso: Строка с длительностью в формате ISO 8601.
        
    Returns:
        str: Отформатированная строка времени.
    """
    if not duration_iso or not duration_iso.startswith('PT'):
        return 'Н/Д'
        
    duration = duration_iso[2:]  # Убираем 'PT'
    hours, minutes, seconds = 0, 0, 0
    
    # Находим часы, если они есть
    if 'H' in duration:
        hours, duration = duration.split('H')
        hours = int(hours)
    
    # Находим минуты, если они есть
    if 'M' in duration:
        minutes, duration = duration.split('M')
        minutes = int(minutes)
    
    # Находим секунды, если они есть
    if 'S' in duration:
        seconds = int(duration.replace('S', ''))
    
    # Форматируем вывод в зависимости от наличия часов
    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def format_count(count_str):
    """
    Форматирует числовое значение для лучшей читаемости.
    
    Args:
        count_str: Строка с числом.
        
    Returns:
        str: Отформатированное число.
    """
    if not count_str:
        return 'Н/Д'
        
    count = int(count_str)
    
    if count >= 1000000:
        return f"{count/1000000:.1f}M"
    elif count >= 1000:
        return f"{count/1000:.1f}K"
    else:
        return str(count)

def print_video_info(video, index=None):
    """
    Выводит информацию о видео в консоль.
    
    Args:
        video: Словарь с информацией о видео.
        index: Опциональный индекс для нумерации результатов.
    """
    prefix = f"{index}. " if index is not None else ""
    
    print(f"\n{prefix}\033[1m{video['title']}\033[0m")
    print(f"   URL: {video['url']}")
    print(f"   Канал: {video['channel_title']}")
    print(f"   Опубликовано: {video['published_at'][:10]}")
    
    # Длительность видео
    duration = format_duration(video.get('duration', ''))
    print(f"   Длительность: {duration}")
    
    # Статистика
    if 'statistics' in video:
        views = format_count(video['statistics'].get('viewCount', '0'))
        likes = format_count(video['statistics'].get('likeCount', '0'))
        print(f"   Просмотры: {views}, Лайки: {likes}")
    
    # Краткое описание (первые 100 символов)
    description = video.get('description', '')
    if description:
        description_preview = description[:100] + "..." if len(description) > 100 else description
        print(f"   Описание: {description_preview}")

def search_and_display(args):
    """
    Выполняет поиск видео и выводит результаты.
    
    Args:
        args: Аргументы командной строки.
    """
    try:
        print(f"Поиск видео по запросу: {args.query}")
        
        # Получаем и отображаем результаты поиска
        results = search_videos(
            query=args.query,
            max_results=args.limit,
            region_code=args.region,
            relevance_language=args.language,
            order=args.order
        )
        
        if not results:
            print("По вашему запросу ничего не найдено.")
            return
            
        print(f"\nНайдено {len(results)} видео:")
        
        for i, video in enumerate(results, 1):
            print_video_info(video, i)
            
        # Предлагаем выбрать видео для загрузки
        if args.download:
            prompt_for_download(results)
        elif not args.no_prompt:
            print("\nДля загрузки видео используйте параметр --download или укажите -d")
            
    except HttpError as e:
        print(f"Ошибка API YouTube: {e}")
    except ValueError as e:
        print(f"Ошибка: {e}")
    except Exception as e:
        print(f"Произошла непредвиденная ошибка: {e}")

def show_channel_info(args):
    """
    Отображает информацию о канале.
    
    Args:
        args: Аргументы командной строки.
    """
    try:
        channel_id = args.channel_id
        
        # Получаем информацию о канале
        channel_info = get_channel_details(channel_id)
        
        if not channel_info:
            print(f"Канал с ID {channel_id} не найден.")
            return
            
        print(f"\n\033[1mИнформация о канале '{channel_info['title']}'\033[0m")
        print(f"URL: {channel_info['url']}")
        print(f"Подписчиков: {format_count(channel_info['statistics']['subscriber_count'])}")
        print(f"Всего просмотров: {format_count(channel_info['statistics']['view_count'])}")
        print(f"Количество видео: {channel_info['statistics']['video_count']}")
        
        if args.videos:
            print(f"\nПолучение видео канала...")
            
            # Получаем и отображаем видео канала
            channel_videos = get_channel_videos(channel_id, max_results=args.limit)
            
            if not channel_videos:
                print("Видео не найдены.")
                return
                
            print(f"\nПоследние {len(channel_videos)} видео канала:")
            
            for i, video in enumerate(channel_videos, 1):
                print_video_info(video, i)
                
            # Предлагаем выбрать видео для загрузки
            if args.download:
                prompt_for_download(channel_videos)
            elif not args.no_prompt:
                print("\nДля загрузки видео используйте параметр --download или укажите -d")
        
    except HttpError as e:
        print(f"Ошибка API YouTube: {e}")
    except ValueError as e:
        print(f"Ошибка: {e}")
    except Exception as e:
        print(f"Произошла непредвиденная ошибка: {e}")

def prompt_for_download(videos):
    """
    Предлагает пользователю выбрать видео для загрузки.
    
    Args:
        videos: Список видео для выбора.
    """
    if not videos:
        print("Нет доступных видео для загрузки.")
        return
        
    print("\nВыберите номер видео для загрузки (или 'q' для выхода):")
    choice = input("> ").strip()
    
    if choice.lower() == 'q':
        return
        
    try:
        idx = int(choice) - 1
        if 0 <= idx < len(videos):
            selected_video = videos[idx]
            url = selected_video['url']
            
            print(f"\nЗапуск обработки для видео: {selected_video['title']}")
            
            # Запускаем src/main.py с URL выбранного видео
            # Используем относительный путь к main.py из корня проекта
            # PROJECT_ROOT_FOR_EXAMPLES уже определен выше
            main_script_path = os.path.join(PROJECT_ROOT_FOR_EXAMPLES, "src", "main.py")
            
            # Формируем команду для запуска python -m src.main <url>
            # Однако, os.system с `python -m` может быть не всегда надежен в разных окружениях.
            # Более прямой вызов модуля, если main.py это позволяет:
            # Или же, если main.py в src/main.py и мы добавили PROJECT_ROOT_FOR_EXAMPLES в sys.path,
            # мы могли бы импортировать main функцию: from src.main import main as run_main_pipeline
            # и вызвать run_main_pipeline(url) - это было бы чище.
            # Пока оставим os.system, но это место для улучшения.
            
            # Важно: main.py должен быть исполняемым и правильно обрабатывать аргументы
            command = f"{sys.executable} {main_script_path} {url}" # Убираем флаг --use-api для теста
            print(f"Выполнение команды: {command}")
            
            os.system(command)
        else:
            print("Неверный номер видео.")
    except ValueError:
        print("Пожалуйста, введите числовой номер видео.")

def main():
    parser = argparse.ArgumentParser(description="Поиск и управление видео на YouTube.")
    subparsers = parser.add_subparsers(dest='command', help='Команда')
    
    # Команда search для поиска видео
    search_parser = subparsers.add_parser('search', help='Поиск видео на YouTube')
    search_parser.add_argument('query', help='Поисковый запрос')
    search_parser.add_argument('-l', '--limit', type=int, default=10, help='Максимальное количество результатов (по умолчанию: 10)')
    search_parser.add_argument('-r', '--region', default='RU', help='Код региона (по умолчанию: RU)')
    search_parser.add_argument('-lang', '--language', default='ru', help='Язык для релевантности (по умолчанию: ru)')
    search_parser.add_argument('-o', '--order', default='relevance', 
                               choices=['date', 'rating', 'relevance', 'title', 'videoCount', 'viewCount'],
                               help='Порядок сортировки (по умолчанию: relevance)')
    search_parser.add_argument('-d', '--download', action='store_true', help='Предложить загрузку после поиска')
    search_parser.add_argument('-n', '--no-prompt', action='store_true', help='Не предлагать загрузку')
    
    # Команда channel для получения информации о канале
    channel_parser = subparsers.add_parser('channel', help='Получить информацию о канале YouTube')
    channel_parser.add_argument('channel_id', help='ID канала YouTube')
    channel_parser.add_argument('-v', '--videos', action='store_true', help='Получить видео канала')
    channel_parser.add_argument('-l', '--limit', type=int, default=10, help='Максимальное количество видео (по умолчанию: 10)')
    channel_parser.add_argument('-d', '--download', action='store_true', help='Предложить загрузку после получения видео')
    channel_parser.add_argument('-n', '--no-prompt', action='store_true', help='Не предлагать загрузку')
    
    # Пока закомментируем команду видео, пока не реализуем YouTubeApiClient
    # # Команда видео для получения детальной информации о видео
    # video_parser = subparsers.add_parser('video', help='Получить подробную информацию о видео YouTube')
    # video_parser.add_argument('video_id', help='ID видео YouTube или полный URL')
    # video_parser.add_argument('-c', '--comments', action='store_true', help='Получить комментарии к видео')
    # video_parser.add_argument('-r', '--related', action='store_true', help='Получить связанные видео')
    # video_parser.add_argument('-s', '--subtitles', action='store_true', help='Получить список доступных субтитров')
    # video_parser.add_argument('-f', '--full', action='store_true', help='Получить полную информацию (включая комментарии, связанные видео и субтитры)')
    # video_parser.add_argument('-d', '--download', action='store_true', help='Предложить загрузку видео')
    # video_parser.add_argument('-n', '--no-prompt', action='store_true', help='Не предлагать загрузку')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
        
    # Убираем пока функцию show_video_info, поскольку она требует YouTubeApiClient
    if args.command == 'search':
        search_and_display(args)
    elif args.command == 'channel':
        show_channel_info(args)
    # elif args.command == 'video':
    #     show_video_info(args)

def show_video_info(args):
    """
    Отображает подробную информацию о видео.
    
    Args:
        args: Аргументы командной строки.
    """
    try:
        from urllib.parse import urlparse, parse_qs
        
        video_id = args.video_id
        
        # Если передан полный URL, извлекаем ID
        if video_id.startswith('http'):
            parsed_url = urlparse(video_id)
            if parsed_url.hostname in ('www.youtube.com', 'youtube.com', 'm.youtube.com') and parsed_url.path == '/watch':
                query_params = parse_qs(parsed_url.query)
                video_id = query_params.get('v', [None])[0]
            elif parsed_url.hostname == 'youtu.be':
                video_id = parsed_url.path.lstrip('/')
                
        if not video_id:
            print("Некорректный формат URL или ID видео.")
            return
            
        # Используем клиент API для получения информации
        api_client = YouTubeApiClient()
        
        if args.full:
            # Получаем полную информацию о видео, включая комментарии, связанные видео и субтитры
            print(f"Получение полной информации о видео {video_id}...")
            video_data = api_client.get_complete_video_data(video_id)
        else:
            # Получаем базовую информацию о видео
            print(f"Получение информации о видео {video_id}...")
            video_data = api_client.get_video_details(video_id)
            
            # Дополнительно получаем комментарии, если запрошены
            if args.comments:
                try:
                    print("Получение комментариев...")
                    comments = api_client.get_video_comments(video_id, max_results=20)
                    video_data['comments'] = comments
                except Exception as e:
                    print(f"Ошибка при получении комментариев: {e}")
                    video_data['comments'] = []
                    
            # Дополнительно получаем связанные видео, если запрошены
            if args.related:
                try:
                    print("Получение связанных видео...")
                    related_videos = api_client.get_related_videos(video_id, max_results=10)
                    video_data['related_videos'] = related_videos
                except Exception as e:
                    print(f"Ошибка при получении связанных видео: {e}")
                    video_data['related_videos'] = []
                    
            # Дополнительно получаем субтитры, если запрошены
            if args.subtitles:
                try:
                    print("Получение списка субтитров...")
                    captions = api_client.get_video_captions(video_id)
                    video_data['captions'] = captions
                except Exception as e:
                    print(f"Ошибка при получении субтитров: {e}")
                    video_data['captions'] = []
        
        if not video_data:
            print(f"Видео с ID {video_id} не найдено.")
            return
            
        # Отображаем подробную информацию о видео
        print(f"\n\033[1mИнформация о видео '{video_data['title']}'\033[0m")
        print(f"URL: {video_data['url']}")
        print(f"Канал: {video_data['channel_title']} ({video_data['channel_id']})")
        print(f"Опубликовано: {video_data['published_at'][:10]}")
        print(f"Длительность: {video_data['duration_formatted']} ({video_data['duration_seconds']} сек)")
        print(f"Просмотров: {video_data['view_count']}")
        print(f"Лайков: {video_data['like_count']}")
        print(f"Комментариев: {video_data['comment_count']}")
        
        # Отображаем описание видео
        description = video_data['description']
        print("\n\033[1mОписание:\033[0m")
        if len(description) > 500:
            print(f"{description[:500]}...")
            print(f"(описание сокращено, полное описание содержит {len(description)} символов)")
        else:
            print(description)
            
        # Отображаем теги, если они есть
        if 'tags' in video_data and video_data['tags']:
            print("\n\033[1mТеги:\033[0m")
            print(", ".join(video_data['tags'][:10]))
            if len(video_data['tags']) > 10:
                print(f"(показано 10 из {len(video_data['tags'])} тегов)")
                
        # Отображаем информацию о субтитрах, если они есть
        if 'captions' in video_data and video_data['captions']:
            print("\n\033[1mДоступные субтитры:\033[0m")
            for i, caption in enumerate(video_data['captions'], 1):
                print(f"{i}. {caption['language']} - {caption['name']} {'(автоматически сгенерированные)' if caption['is_auto_synced'] else ''}")
                
        # Отображаем комментарии, если они есть
        if 'comments' in video_data and video_data['comments']:
            print(f"\n\033[1mКомментарии ({len(video_data['comments'])} загружено):\033[0m")
            for i, comment in enumerate(video_data['comments'][:5], 1):
                print(f"\n{i}. {comment['author_display_name']} ({comment['published_at'][:10]}):")
                comment_text = comment['text']
                if len(comment_text) > 200:
                    print(f"   {comment_text[:200]}...")
                else:
                    print(f"   {comment_text}")
                    
                if comment['reply_count'] > 0 and comment['replies']:
                    print(f"   Ответов: {comment['reply_count']}")
                    for j, reply in enumerate(comment['replies'][:2], 1):
                        reply_text = reply['text']
                        print(f"     {j}. {reply['author_display_name']}: {reply_text[:100]}..." if len(reply_text) > 100 else f"     {j}. {reply['author_display_name']}: {reply_text}")
                    
                    if len(comment['replies']) > 2:
                        print(f"     ... и еще {len(comment['replies']) - 2} ответов")
                        
            if len(video_data['comments']) > 5:
                print(f"\n(показано 5 из {len(video_data['comments'])} комментариев)")
                
        # Отображаем связанные видео, если они есть
        if 'related_videos' in video_data and video_data['related_videos']:
            print(f"\n\033[1mСвязанные видео ({len(video_data['related_videos'])} загружено):\033[0m")
            for i, video in enumerate(video_data['related_videos'][:5], 1):
                print(f"\n{i}. {video['title']}")
                print(f"   URL: {video['url']}")
                print(f"   Канал: {video['channel_title']}")
                if 'duration_formatted' in video:
                    print(f"   Длительность: {video['duration_formatted']}")
                    
            if len(video_data['related_videos']) > 5:
                print(f"\n(показано 5 из {len(video_data['related_videos'])} связанных видео)")
                
        # Предлагаем скачать видео, если запрошено
        if args.download:
            prompt_for_download([{'id': video_id, 'title': video_data['title'], 'url': video_data['url']}])
        elif not args.no_prompt:
            print("\nДля загрузки видео используйте параметр --download или укажите -d")
            
    except HttpError as e:
        print(f"Ошибка API YouTube: {e}")
    except ValueError as e:
        print(f"Ошибка: {e}")
    except Exception as e:
        print(f"Произошла непредвиденная ошибка: {e}")

if __name__ == '__main__':
    # Проверяем, установлен ли YOUTUBE_API_KEY
    if not os.getenv('YOUTUBE_API_KEY'):
        print("ОШИБКА: API ключ YouTube (YOUTUBE_API_KEY) не найден в переменных окружения.")
        print("Пожалуйста, создайте файл .env с вашим YOUTUBE_API_KEY.")
        sys.exit(1)
        
    main()