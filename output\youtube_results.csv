﻿title,type,id,URL видео,thumbnailUrl,viewCount,date,likes,location,Ник канала,URL канала,ID канала,channelUsername,numberOfSubscribers,duration,commentsCount,text,descriptionLinks,subtitles,order,commentsTurnedOff,comments,fromYTUrl,isMonetized,hashtags,formats,isMembersOnly,input,ID плейлиста,Название плейлиста
AI Agents & MCP: Create autonomous artificial intelligence in 2025,video,DFAW3cwQVmc,https://www.youtube.com/watch?v=DFAW3cwQVmc,https://i.ytimg.com/vi/DFAW3cwQVmc/maxresdefault.jpg,8735,2025-03-28T12:20:55.000Z,403,,LeCoinStat,https://www.youtube.com/@LeCoinStat,UCoincaYNa5FBdi-I5oqHAMw,LeCoinStat,47900,00:24:19,44,"In this video, I show you how to design an autonomous AI agent capable of performing specific tasks (writing and sending an email, connecting to tools like Gmail, Notion, or LinkedIn, etc.) without any human intervention.

We'll also explore a key concept for 2025: MCP (Model-Context-Protocol), the key to connecting AI to the outside world.

Download your MCP guide here: https://natacha-njongwa-yepnga.kit.co...
In the program:
00:00 – Why AI agents are essential in 2025
00:45 – Simple example: writing an email with ChatGPT
01:55 – Limitations of ChatGPT without automation
02:20 – Introduction to AI agents and n8n (low-code)
03:50 – Demo: an AI agent that automatically writes AND sends the email
05:00 – What is an MCP
08:00 – Concrete example of a connected AI agent
10:30 – Claude + MCP: autonomous AI that explores the Internet on its own
14:00 – Demo: Claude conducts a complete market study
17:00 – Creating an interface in React with the help of AI
20:00 – Integrating MCPs into Cursor
23:00 – Bonus Demo: Automatic Code Generation for a Snake Game
24:00 – Conclusion

#n8n #mcp #ia #agentia #lecoinstat

--------------------------------------
🚨 Relevant Links 🚨

My newsletter: https://natacha-njongwa-yepnga.ck.pag...
YouTube: https://www.youtube.com/c/LeCoinStat?...
Linkedin:   / natacha-njongwa-yepnga  
Discord:   / discord  
TikTok: https://vm.tiktok.com/ZMLEgAhku/
Instagram:   / lecoin_stat  
Facebook:   / lecoinstat  ","[{'url': 'https://natacha-njongwa-yepnga.kit.com/mcp', 'text': 'https://natacha-njongwa-yepnga.kit.co...'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc', 'text': '00:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=45s', 'text': '00:45'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=115s', 'text': '01:55'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=140s', 'text': '02:20'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=230s', 'text': '03:50'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=300s', 'text': '05:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=480s', 'text': '08:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=630s', 'text': '10:30'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=840s', 'text': '14:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=1020s', 'text': '17:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=1200s', 'text': '20:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=1380s', 'text': '23:00'}, {'url': 'https://www.youtube.com/watch?v=DFAW3cwQVmc&t=1440s', 'text': '24:00'}, {'url': 'https://www.youtube.com/hashtag/n8n', 'text': '#n8n'}, {'url': 'https://www.youtube.com/hashtag/mcp', 'text': '#mcp'}, {'url': 'https://www.youtube.com/hashtag/ia', 'text': '#ia'}, {'url': 'https://www.youtube.com/hashtag/agentia', 'text': '#agentia'}, {'url': 'https://www.youtube.com/hashtag/lecoinstat', 'text': '#lecoinstat'}, {'url': 'https://natacha-njongwa-yepnga.ck.page/inscriptionnewsletter', 'text': 'https://natacha-njongwa-yepnga.ck.pag...'}, {'url': 'https://www.youtube.com/c/LeCoinStat?sub_confirmation=1', 'text': 'https://www.youtube.com/c/LeCoinStat?...'}, {'url': 'https://www.linkedin.com/in/natacha-njongwa-yepnga/', 'text': '\xa0\xa0/\xa0natacha-njongwa-yepnga\xa0\xa0'}, {'url': 'https://discord.com/invite/RpyvkR7SfQ', 'text': '\xa0\xa0/\xa0discord\xa0\xa0'}, {'url': 'https://vm.tiktok.com/ZMLEgAhku/', 'text': 'https://vm.tiktok.com/ZMLEgAhku/'}, {'url': 'https://www.instagram.com/lecoin_stat/', 'text': '\xa0\xa0/\xa0lecoin_stat\xa0\xa0'}, {'url': 'https://www.facebook.com/LeCoinStat', 'text': '\xa0\xa0/\xa0lecoinstat\xa0\xa0'}]",,5,False,,https://www.youtube.com/results?search_query=ia+agents+mcp,,"['#n8n', '#mcp', '#agentia']",[],False,ia agents mcp,,
Model Context Protocol (MCP): The Key To Agentic AI,video,VChRPFUzJGA,https://www.youtube.com/watch?v=VChRPFUzJGA,https://i.ytimg.com/vi/VChRPFUzJGA/maxresdefault.jpg,107101,2025-03-17T14:34:30.000Z,3900,,Jack Herrington,https://www.youtube.com/@jherr,UC6vRUjYqDuoUsYsku86Lrsw,jherr,202000,00:10:05,136,"The Model Content Protocol is taking the AI world by storm. Find out what MCP is, what it can be used for, how to create and use MCP servers, and much more!

In-Depth video:    • Applied Model Context Protocol (MCP) ...  

https://www.pulsemcp.com/clients
https://www.pulsemcp.com/servers

👉 ProNextJS Course: https://pronextjs.dev
👉 Don't forget to subscribe to this channel for more updates: https://bit.ly/2E7drfJ
👉 Discord server signup:   / discord  
👉 VS Code theme and font? Night Wolf [black] and BitstromWera Nerd Font Mono
👉 Terminal Theme and font? oh-my-posh with custom prompt and BitstromWera Nerd Font Mono

00:00 Introduction
00:23 M Is For Model
01:03 C Is For Context
02:14 MCP Clients & Servers
03:00 P Is For Protocol
03:34 MCP Transports
04:31 Trying MCP Out
05:38 Building An MCP Server
07:32 API Standards Comparison
08:42 Key Takeaways
09:43 Outroduction","[{'url': 'https://www.youtube.com/watch?v=eD0uBLr-eP8', 'text': '\xa0\xa0\xa0•\xa0Applied\xa0Model\xa0Context\xa0Protocol\xa0(MCP)\xa0...\xa0\xa0'}, {'url': 'https://www.pulsemcp.com/clients', 'text': 'https://www.pulsemcp.com/clients'}, {'url': 'https://www.pulsemcp.com/servers', 'text': 'https://www.pulsemcp.com/servers'}, {'url': 'https://pronextjs.dev/', 'text': 'https://pronextjs.dev'}, {'url': 'https://bit.ly/2E7drfJ', 'text': 'https://bit.ly/2E7drfJ'}, {'url': 'https://discord.gg/ddMZFtTDa5', 'text': '\xa0\xa0/\xa0discord\xa0\xa0'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA', 'text': '00:00'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=23s', 'text': '00:23'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=63s', 'text': '01:03'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=134s', 'text': '02:14'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=180s', 'text': '03:00'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=214s', 'text': '03:34'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=271s', 'text': '04:31'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=338s', 'text': '05:38'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=452s', 'text': '07:32'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=522s', 'text': '08:42'}, {'url': 'https://www.youtube.com/watch?v=VChRPFUzJGA&t=583s', 'text': '09:43'}]",,1,False,,https://www.youtube.com/results?search_query=ia+agents+mcp,,[],[],False,ia agents mcp,,
I Made MCP AI Agents That Automate Every App I Build.,video,23ykmgvaSmM,https://www.youtube.com/watch?v=23ykmgvaSmM,https://i.ytimg.com/vi/23ykmgvaSmM/maxresdefault.jpg,24513,2025-05-01T20:27:21.000Z,892,,AI LABS,https://www.youtube.com/@AILABS-393,UCelfWQr9sXVMTvBzviPGlFw,AILABS-393,18900,00:08:30,45,"In this deep-dive I explain how i made mcp ai agents that automate every app i build, showing step-by-step how a simple FastAPI backend becomes a fully voice-controlled workspace once it’s wrapped in FastAPI MCP. You’ll see the Model Context Protocol (MCP) in action—from spinning up an MCP server, naming tools, and wiring them into Cursor, to building a standalone AI agent that issues live commands. Along the way we answer “what is MCP?”, compare MCP servers like Cursor, Cloud-Desktop and mcp n8n, and test interoperability with ChatGPT and Claude MCP. Whether you’re here for a practical mcp tutorial, researching mcp ai agents for production, or just curious how ai agents can call any endpoint you expose, this walkthrough shows the exact code, pitfalls, and fixes—no hype, no skipped steps.

🔗  Repos & Docs

• FastAPI MCP example → https://github.com/tadata-org/fastapi...
• MCP-Use framework → https://github.com/mcp-use/mcp-use

By the end you’ll understand how to:
 • expose every route in your project as a usable tool with fastapi mcp,
 • register and rename operations so LLMs pick the right function first try,
 • plug multiple MCP servers into a single config for multi-app orchestration,
 • script an AI agent that speaks natural language and controls your data in real time, and
 • extend the pattern to cloud or on-prem apps with zero UI rebuild.

If phrases like “ai agent CRUD by voice” spark ideas, hit play—this is your complete, hands-on launch pad for building production-grade, conversational automation with MCP, ai, and the frameworks you already use.","[{'url': 'https://github.com/tadata-org/fastapi_mcp', 'text': 'https://github.com/tadata-org/fastapi...'}, {'url': 'https://github.com/mcp-use/mcp-use', 'text': 'https://github.com/mcp-use/mcp-use'}]",,6,False,,https://www.youtube.com/results?search_query=ia+agents+mcp,,[],[],False,ia agents mcp,,
Build Anything with MCP Agents… Here’s How,video,L94WBLL0KjY,https://www.youtube.com/watch?v=L94WBLL0KjY,https://i.ytimg.com/vi/L94WBLL0KjY/maxresdefault.jpg,208514,2025-03-27T15:00:25.000Z,5400,,Tech With Tim,https://www.youtube.com/@TechWithTim,UC4JX40jDee_tINbkjycV4Sg,TechWithTim,1750000,00:20:13,176,"Check out SambaNova’s Deep Research Agents here: https://fnf.dev/4kwvYRU

Let's talk about MCP or the Model Context Protocol. This has been blowing up recently. A ton of people are covering it, especially on YouTube, but unfortunately, most people talking about this have no idea what it actually is, how it works, and how to explain it in a way where they're not going to confuse the heck out of you. I'm going to break down what it is in very simple terms, coming from someone who is actually a developer.

🚀 My Software Development Program: https://coursecareers.com/a/techwitht...

📬 Join my Newsletter: https://techwithtim.net/newsletter

🎞 Video Resources 🎞
NodeJS Install: https://nodejs.org/en/download
MCP Server Docs: https://docs.cursor.com/context/model...
Coincap MCP Server: https://github.com/QuantGeekDev/coinc...
Firecrawl API Site: https://www.firecrawl.dev/app?authSuc....
MCP Server List (GitHub): https://github.com/punkpeye/awesome-m...
Smithery MCP Directory: https://smithery.ai/
Sequential Thinking Smithery Page: https://smithery.ai/server/@smithery-...
Firecrawl MCP Server Page: https://mcp.so/server/firecrawl-mcp-s...

⏳ Timestamps ⏳
00:00 | Overview
00:34 | Understanding Protocols & Standards
03:54 | MCP Explained
07:04 | Installing Node.js
07:50 | Adding MCP Servers to Cursor
16:32 | Installing from Smithery
17:22 | Installing Non Documented MCP Servers
18:54 | Auto Calling Tools

Hashtags
#MCP #CursorAI #SambaNova","[{'url': 'https://fnf.dev/4kwvYRU', 'text': 'https://fnf.dev/4kwvYRU'}, {'url': 'https://coursecareers.com/a/techwithtim?course=software-dev-fundamentals&campaign=youtubedescription', 'text': 'https://coursecareers.com/a/techwitht...'}, {'url': 'https://techwithtim.net/newsletter', 'text': 'https://techwithtim.net/newsletter'}, {'url': 'https://nodejs.org/en/download', 'text': 'https://nodejs.org/en/download'}, {'url': 'https://docs.cursor.com/context/model-context-protocol', 'text': 'https://docs.cursor.com/context/model...'}, {'url': 'https://github.com/QuantGeekDev/coincap-mcp', 'text': 'https://github.com/QuantGeekDev/coinc...'}, {'url': 'https://www.firecrawl.dev/app?authSuccess=true&?status=Success!&status_description=You%20are%20now%20signed%20in', 'text': 'https://www.firecrawl.dev/app?authSuc...'}, {'url': 'https://github.com/punkpeye/awesome-mcp-servers?tab=readme-ov-file#file-systems', 'text': 'https://github.com/punkpeye/awesome-m...'}, {'url': 'https://smithery.ai/', 'text': 'https://smithery.ai/'}, {'url': 'https://smithery.ai/server/@smithery-ai/server-sequential-thinking', 'text': 'https://smithery.ai/server/@smithery-...'}, {'url': 'https://mcp.so/server/firecrawl-mcp-server/mendableai', 'text': 'https://mcp.so/server/firecrawl-mcp-s...'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY', 'text': '00:00'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=34s', 'text': '00:34'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=234s', 'text': '03:54'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=424s', 'text': '07:04'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=470s', 'text': '07:50'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=992s', 'text': '16:32'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=1042s', 'text': '17:22'}, {'url': 'https://www.youtube.com/watch?v=L94WBLL0KjY&t=1134s', 'text': '18:54'}, {'url': 'https://www.youtube.com/hashtag/mcp', 'text': '#MCP'}, {'url': 'https://www.youtube.com/hashtag/cursorai', 'text': '#CursorAI'}, {'url': 'https://www.youtube.com/hashtag/sambanova', 'text': '#SambaNova'}]",,3,False,,https://www.youtube.com/results?search_query=ia+agents+mcp,,"['#CursorAI', '#MCP', '#SambaNova']",[],False,ia agents mcp,,
What is MCP? Integrate AI Agents with Databases & APIs,video,eur8dUO9mvE,https://www.youtube.com/watch?v=eur8dUO9mvE,https://i.ytimg.com/vi/eur8dUO9mvE/maxresdefault.jpg,141305,2025-02-19T12:00:59.000Z,3800,,IBM Technology,https://www.youtube.com/@IBMTechnology,UCKWaEZ-_VweaEx1j62do_vQ,IBMTechnology,1190000,00:03:46,105,"Ready to become a certified Architect on Cloud Pak? Register now and use code IBMTechYT20 for 20% off of your exam → https://ibm.biz/BdGhLq

Learn more about AI Agents here → https://ibm.biz/BdGhLf

Unlock the secrets of MCP! 🚀 Dive into the world of Model Context Protocol and learn how to seamlessly connect AI agents to databases, APIs, and more. Roy Derks breaks down its components, from hosts to servers, and showcases real-world applications. Gain the knowledge to revolutionize your AI projects! 🧠

AI news moves fast. Sign up for a monthly newsletter for AI updates from IBM → https://ibm.biz/BdGhLP

#aiagents #dataintegration #api","[{'url': 'https://ibm.biz/BdGhLq', 'text': 'https://ibm.biz/BdGhLq'}, {'url': 'https://ibm.biz/BdGhLf', 'text': 'https://ibm.biz/BdGhLf'}, {'url': 'https://ibm.biz/BdGhLP', 'text': 'https://ibm.biz/BdGhLP'}, {'url': 'https://www.youtube.com/hashtag/aiagents', 'text': '#aiagents'}, {'url': 'https://www.youtube.com/hashtag/dataintegration', 'text': '#dataintegration'}, {'url': 'https://www.youtube.com/hashtag/api', 'text': '#api'}]",,0,False,,https://www.youtube.com/results?search_query=ia+agents+mcp,,"['#aiagents', '#api', '#dataintegration']",[],False,ia agents mcp,,
