def show_video_info(args):
    """
    Отображает подробную информацию о видео.
    
    Args:
        args: Аргументы командной строки.
    """
    try:
        video_id = args.video_id
        
        # Если передан полный URL, извлекаем ID
        if video_id.startswith('http'):
            parsed_url = urlparse(video_id)
            if parsed_url.hostname in ('www.youtube.com', 'youtube.com', 'm.youtube.com') and parsed_url.path == '/watch':
                query_params = parse_qs(parsed_url.query)
                video_id = query_params.get('v', [None])[0]
            elif parsed_url.hostname == 'youtu.be':
                video_id = parsed_url.path.lstrip('/')
                
        if not video_id:
            print("Некорректный формат URL или ID видео.")
            return
            
        # Используем клиент API для получения информации
        api_client = YouTubeApiClient()
        
        if args.full:
            # Получаем полную информацию о видео, включая комментарии, связанные видео и субтитры
            print(f"Получение полной информации о видео {video_id}...")
            video_data = api_client.get_complete_video_data(video_id)
        else:
            # Получаем базовую информацию о видео
            print(f"Получение информации о видео {video_id}...")
            video_data = api_client.get_video_details(video_id)
            
            # Дополнительно получаем комментарии, если запрошены
            if args.comments:
                try:
                    print("Получение комментариев...")
                    comments = api_client.get_video_comments(video_id, max_results=20)
                    video_data['comments'] = comments
                except Exception as e:
                    print(f"Ошибка при получении комментариев: {e}")
                    video_data['comments'] = []
                    
            # Дополнительно получаем связанные видео, если запрошены
            if args.related:
                try:
                    print("Получение связанных видео...")
                    related_videos = api_client.get_related_videos(video_id, max_results=10)
                    video_data['related_videos'] = related_videos
                except Exception as e:
                    print(f"Ошибка при получении связанных видео: {e}")
                    video_data['related_videos'] = []
                    
            # Дополнительно получаем субтитры, если запрошены
            if args.subtitles:
                try:
                    print("Получение списка субтитров...")
                    captions = api_client.get_video_captions(video_id)
                    video_data['captions'] = captions
                except Exception as e:
                    print(f"Ошибка при получении субтитров: {e}")
                    video_data['captions'] = []
        
        if not video_data:
            print(f"Видео с ID {video_id} не найдено.")
            return
            
        # Отображаем подробную информацию о видео
        print(f"\n\033[1mИнформация о видео '{video_data['title']}'\033[0m")
        print(f"URL: {video_data['url']}")
        print(f"Канал: {video_data['channel_title']} ({video_data['channel_id']})")
        print(f"Опубликовано: {video_data['published_at'][:10]}")
        print(f"Длительность: {video_data['duration_formatted']} ({video_data['duration_seconds']} сек)")
        print(f"Просмотров: {video_data['view_count']}")
        print(f"Лайков: {video_data['like_count']}")
        print(f"Комментариев: {video_data['comment_count']}")
        
        # Отображаем описание видео
        description = video_data['description']
        print("\n\033[1mОписание:\033[0m")
        if len(description) > 500:
            print(f"{description[:500]}...")
            print("(описание сокращено, полное описание содержит {len(description)} символов)")
        else:
            print(description)
            
        # Отображаем теги, если они есть
        if 'tags' in video_data and video_data['tags']:
            print("\n\033[1mТеги:\033[0m")
            print(", ".join(video_data['tags'][:10]))
            if len(video_data['tags']) > 10:
                print(f"(показано 10 из {len(video_data['tags'])} тегов)")
                
        # Отображаем информацию о субтитрах, если они есть
        if 'captions' in video_data and video_data['captions']:
            print("\n\033[1mДоступные субтитры:\033[0m")
            for i, caption in enumerate(video_data['captions'], 1):
                print(f"{i}. {caption['language']} - {caption['name']} {'(автоматически сгенерированные)' if caption['is_auto_synced'] else ''}")
                
        # Отображаем комментарии, если они есть
        if 'comments' in video_data and video_data['comments']:
            print(f"\n\033[1mКомментарии ({len(video_data['comments'])} загружено):\033[0m")
            for i, comment in enumerate(video_data['comments'][:5], 1):
                print(f"\n{i}. {comment['author_display_name']} ({comment['published_at'][:10]}):")
                comment_text = comment['text']
                if len(comment_text) > 200:
                    print(f"   {comment_text[:200]}...")
                else:
                    print(f"   {comment_text}")
                    
                if comment['reply_count'] > 0 and comment['replies']:
                    print(f"   Ответов: {comment['reply_count']}")
                    for j, reply in enumerate(comment['replies'][:2], 1):
                        reply_text = reply['text']
                        print(f"     {j}. {reply['author_display_name']}: {reply_text[:100]}..." if len(reply_text) > 100 else f"     {j}. {reply['author_display_name']}: {reply_text}")
                    
                    if len(comment['replies']) > 2:
                        print(f"     ... и еще {len(comment['replies']) - 2} ответов")
                        
            if len(video_data['comments']) > 5:
                print(f"\n(показано 5 из {len(video_data['comments'])} комментариев)")
                
        # Отображаем связанные видео, если они есть
        if 'related_videos' in video_data and video_data['related_videos']:
            print(f"\n\033[1mСвязанные видео ({len(video_data['related_videos'])} загружено):\033[0m")
            for i, video in enumerate(video_data['related_videos'][:5], 1):
                print(f"\n{i}. {video['title']}")
                print(f"   URL: {video['url']}")
                print(f"   Канал: {video['channel_title']}")
                if 'duration_formatted' in video:
                    print(f"   Длительность: {video['duration_formatted']}")
                    
            if len(video_data['related_videos']) > 5:
                print(f"\n(показано 5 из {len(video_data['related_videos'])} связанных видео)")
                
        # Предлагаем скачать видео, если запрошено
        if args.download:
            prompt_for_download([{'id': video_id, 'title': video_data['title'], 'url': video_data['url']}])
        elif not args.no_prompt:
            print("\nДля загрузки видео используйте параметр --download или укажите -d")
            
    except HttpError as e:
        print(f"Ошибка API YouTube: {e}")
    except ValueError as e:
        print(f"Ошибка: {e}")
    except Exception as e:
        print(f"Произошла непредвиденная ошибка: {e}")