
from apify_client import ApifyClient

# Простой скрипт для тестирования API
print("Скрипт запущен")

try:
    # Инициализация клиента
    client = ApifyClient(token='**********************************************')
    print("Клиент инициализирован")

    # Получаем информацию о себе
    print("Получение информации о пользователе...")
    user_info = client.user().get()
    print(f"ID пользователя: {user_info.get('userId')}")
    print(f"Имя пользователя: {user_info.get('username')}")

    print("Скрипт завершен успешно")
except Exception as e:
    print(f"Произошла ошибка: {str(e)}")
