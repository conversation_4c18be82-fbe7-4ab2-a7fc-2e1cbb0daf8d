[{"title": "All Machine Learning algorithms explained in 17 min", "type": "video", "id": "E0Hmnixke2g", "url": "https://www.youtube.com/watch?v=E0Hmnixke2g", "thumbnailUrl": "https://i.ytimg.com/vi/E0Hmnixke2g/maxresdefault.jpg", "viewCount": 875622, "date": "2024-09-17T08:57:32.000Z", "likes": 36000, "location": null, "channelName": "Infinite Codes", "channelUrl": "https://www.youtube.com/@InfiniteCodes_", "channelId": "UCktMIWomeuj4pwvBb-OBwMw", "channelUsername": "InfiniteCodes_", "numberOfSubscribers": 78800, "duration": "00:16:30", "commentsCount": 289, "text": "All Machine Learning algorithms intuitively explained in 17 min\n\n#########################################\nI just started my own Patreon, in case you want to support!\nPatreon Link:   / infinitecodes  \n#########################################\n\nIn this video I will go through all machine learning algorithms in less than 17 minutes to get you an intuitive understanding of how they work and how they relate to each other as well as help you decide how to pick the right one for your problem. Going all the way from Linear Regression to Neural Networks / Deep Learning and Unsupervised Learning.\n\nAlso Watch: \nHow to Learn Machine Learning in 2024 (7 step roadmap)    • How to Learn Machine Learning in 2024...  \nThe Math Skills that make Machine Learning easy (and how you can learn it)    • How Math makes Machine Learning easy ...  \n\n#########\nChapters:\n\n00:00 - Intro: What is Machine Learning?\n00:59 - Supervised Learning\n01:37 - Unsupervised Learning\n02:20 - Linear Regression\n04:04 - Logistic Regression\n04:53 - K Nearest Neighbors (KNN)\n06:10 - Support Vector Machine (SVM)\n07:51 - <PERSON><PERSON> Classifier\n08:37 - Decision Trees\n09:11 - Ensemble Algorithms\n09:24 - Bagging & Random Forests\n09:53 - Boosting & Strong Learners\n10:26 - Neural Networks / Deep Learning\n12:42 - Unsupervised Learning (again)\n12:57 - Clustering / K-means\n14:35 - Dimensionality Reduction\n15:15 - Principal Component Analysis (PCA)", "descriptionLinks": [{"url": "https://www.patreon.com/c/InfiniteCodes", "text": "  / infinitecodes  "}, {"url": "https://www.youtube.com/watch?v=jwTaBztqTZ0", "text": "   • How to Learn Machine Learning in 2024...  "}, {"url": "https://www.youtube.com/watch?v=wOTFGRSUQ6Q", "text": "   • How Math makes Machine Learning easy ...  "}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g", "text": "00:00"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=59s", "text": "00:59"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=97s", "text": "01:37"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=140s", "text": "02:20"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=244s", "text": "04:04"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=293s", "text": "04:53"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=370s", "text": "06:10"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=471s", "text": "07:51"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=517s", "text": "08:37"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=551s", "text": "09:11"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=564s", "text": "09:24"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=593s", "text": "09:53"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=626s", "text": "10:26"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=762s", "text": "12:42"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=777s", "text": "12:57"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=875s", "text": "14:35"}, {"url": "https://www.youtube.com/watch?v=E0Hmnixke2g&t=915s", "text": "15:15"}], "subtitles": null, "order": 0, "commentsTurnedOff": false, "comments": null, "fromYTUrl": "https://www.youtube.com/results?search_query=machine+learning", "isMonetized": null, "hashtags": [], "formats": [], "isMembersOnly": false, "input": "machine learning"}, {"title": "Machine Learning for Everybody – Full Course", "type": "video", "id": "i_LwzRVP7bg", "url": "https://www.youtube.com/watch?v=i_LwzRVP7bg", "thumbnailUrl": "https://i.ytimg.com/vi/i_LwzRVP7bg/maxresdefault.jpg", "viewCount": 8654736, "date": "2022-09-26T16:00:28.000Z", "likes": 87000, "location": null, "channelName": "freeCodeCamp.org", "channelUrl": "https://www.youtube.com/@freecodecamp", "channelId": "UC8butISFwT-Wl7EV0hUK0BQ", "channelUsername": "freecodecamp", "numberOfSubscribers": 10700000, "duration": "03:53:53", "commentsCount": 1924, "text": "Learn Machine Learning in a way that is accessible to absolute beginners. You will learn the basics of Machine Learning and how to use TensorFlow to implement many different concepts.\n\n✏️ <PERSON> developed this course. Check out her channel:    / ycubed  \n\n⭐️ Code and Resources ⭐️\n🔗 Supervised learning (classification/MAGIC): https://colab.research.google.com/dri...\n🔗 Supervised learning (regression/bikes): https://colab.research.google.com/dri...\n🔗 Unsupervised learning (seeds): https://colab.research.google.com/dri...\n🔗 Dataets (add a note that for the bikes dataset, they may have to open the downloaded csv file and remove special characters)\n🔗 MAGIC dataset: https://archive.ics.uci.edu/ml/datase...\n🔗 Bikes dataset: https://archive.ics.uci.edu/ml/datase...\n🔗 Seeds/wheat dataset: https://archive.ics.uci.edu/ml/datase...\n\n🏗 Google provided a grant to make this course possible. \n\n❤️ Support for this channel comes from our friends at Scrimba – the coding platform that's reinvented interactive learning: https://scrimba.com/freecodecamp\n\n⭐️ Contents ⭐️\n⌨️ (0:00:00) Intro\n⌨️ (0:00:58) Data/Colab Intro\n⌨️ (0:08:45) Intro to Machine Learning\n⌨️ (0:12:26) Features\n⌨️ (0:17:23) Classification/Regression\n⌨️ (0:19:57) Training Model\n⌨️ (0:30:57) Preparing Data\n⌨️ (0:44:43) K-Nearest Neighbors\n⌨️ (0:52:42) KNN Implementation\n⌨️ (1:08:43) Naive Bayes\n⌨️ (1:17:30) Naive Bayes Implementation\n⌨️ (1:19:22) Logistic Regression\n⌨️ (1:27:56) Log Regression Implementation\n⌨️ (1:29:13) Support Vector Machine\n⌨️ (1:37:54) SVM Implementation\n⌨️ (1:39:44) Neural Networks\n⌨️ (1:47:57) Tensorflow\n⌨️ (1:49:50) Classification NN using Tensorflow\n⌨️ (2:10:12) Linear Regression\n⌨️ (2:34:54) Lin Regression Implementation\n⌨️ (2:57:44) Lin Regression using a Neuron\n⌨️ (3:00:15) Regression NN using Tensorflow\n⌨️ (3:13:13) K-Means Clustering\n⌨️ (3:23:46) Principal Component Analysis\n⌨️ (3:33:54) K-Means and PCA Implementations\n\n🎉 Thanks to our Champion and Sponsor supporters:\n👾 Raymond Odero\n👾 Agustín Kussrow\n👾 aldo ferretti\n👾 Otis Morgan\n👾 DeezMaster\n\n--\n\nLearn to code for free and get a developer job: https://www.freecodecamp.org\n\nRead hundreds of articles on programming: https://freecodecamp.org/news", "descriptionLinks": [{"url": "https://www.youtube.com/c/YCubed", "text": "   / ycubed  "}, {"url": "https://colab.research.google.com/drive/16w3TDn_tAku17mum98EWTmjaLHAJcsk0?usp=sharing", "text": "https://colab.research.google.com/dri..."}, {"url": "https://colab.research.google.com/drive/1m3oQ9b0oYOT-DXEy0JCdgWPLGllHMb4V?usp=sharing", "text": "https://colab.research.google.com/dri..."}, {"url": "https://colab.research.google.com/drive/1zw_6ZnFPCCh6mWDAd_VBMZB4VkC3ys2q?usp=sharing", "text": "https://colab.research.google.com/dri..."}, {"url": "https://archive.ics.uci.edu/ml/datasets/MAGIC+Gamma+Telescope", "text": "https://archive.ics.uci.edu/ml/datase..."}, {"url": "https://archive.ics.uci.edu/ml/datasets/Seoul+Bike+Sharing+Demand", "text": "https://archive.ics.uci.edu/ml/datase..."}, {"url": "https://archive.ics.uci.edu/ml/datasets/seeds", "text": "https://archive.ics.uci.edu/ml/datase..."}, {"url": "https://scrimba.com/freecodecamp", "text": "https://scrimba.com/freecodecamp"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg", "text": "0:00:00"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=58s", "text": "0:00:58"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=525s", "text": "0:08:45"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=746s", "text": "0:12:26"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=1043s", "text": "0:17:23"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=1197s", "text": "0:19:57"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=1857s", "text": "0:30:57"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=2683s", "text": "0:44:43"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=3162s", "text": "0:52:42"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=4123s", "text": "1:08:43"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=4650s", "text": "1:17:30"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=4762s", "text": "1:19:22"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=5276s", "text": "1:27:56"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=5353s", "text": "1:29:13"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=5874s", "text": "1:37:54"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=5984s", "text": "1:39:44"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=6477s", "text": "1:47:57"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=6590s", "text": "1:49:50"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=7812s", "text": "2:10:12"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=9294s", "text": "2:34:54"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=10664s", "text": "2:57:44"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=10815s", "text": "3:00:15"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=11593s", "text": "3:13:13"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=12226s", "text": "3:23:46"}, {"url": "https://www.youtube.com/watch?v=i_LwzRVP7bg&t=12834s", "text": "3:33:54"}, {"url": "https://www.freecodecamp.org/", "text": "https://www.freecodecamp.org"}, {"url": "https://freecodecamp.org/news", "text": "https://freecodecamp.org/news"}], "subtitles": null, "order": 1, "commentsTurnedOff": false, "comments": null, "fromYTUrl": "https://www.youtube.com/results?search_query=machine+learning", "isMonetized": null, "hashtags": [], "formats": [], "isMembersOnly": false, "input": "machine learning"}, {"title": "Stanford CS229 I Machine Learning I Building Large Language Models (LLMs)", "type": "video", "id": "9vM4p9NN0Ts", "url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts", "thumbnailUrl": "https://i.ytimg.com/vi/9vM4p9NN0Ts/maxresdefault.jpg?sqp=-oaymwEmCIAKENAF8quKqQMa8AEB-AH-CYAC0AWKAgwIABABGGUgZShYMA8=&rs=AOn4CLB9dcbOkEQKsKH0M0OwJhCrYO6E7w", "viewCount": 1049899, "date": "2024-08-27T22:02:57.000Z", "likes": 31000, "location": null, "channelName": "Stanford Online", "channelUrl": "https://www.youtube.com/@stanfordonline", "channelId": "UCBa5G_ESCn8Yd4vw5U-gIcg", "channelUsername": "stanfordonline", "numberOfSubscribers": 773000, "duration": "01:44:31", "commentsCount": 275, "text": "For more information about Stanford's Artificial Intelligence programs visit: https://stanford.io/ai\n\nThis lecture provides a concise overview of building a ChatGPT-like model, covering both pretraining (language modeling) and post-training (SFT/RLHF). For each component, it explores common practices in data collection, algorithms, and evaluation methods. This guest lecture was delivered by <PERSON><PERSON> in Stanford’s CS229: Machine Learning course, in Summer 2024.\n\n<PERSON><PERSON>\nPhD Student at Stanford\nhttps://yanndubs.github.io/\n\nAbout the speaker: <PERSON><PERSON> is a fourth-year CS PhD student advised by <PERSON> and <PERSON><PERSON>. His research focuses on improving the effectiveness of AI when resources are scarce. Most recently, he has been part of the Alpaca team, working on training and evaluating language models more efficiently using other LLMs.\n\nTo view all online courses and programs offered by Stanford, visit: http://online.stanford.edu\n\nChapters:\n00:00 - Introduction  \n00:10 - Recap on LLMs  \n00:16 - Definition of LLMs  \n00:19 - Examples of LLMs  \n01:16 - Importance of Data  \n01:20 - Evaluation Metrics  \n01:33 - Systems Component  \n01:41 - Importance of Systems  \n01:47 - LLMs Based on Transformers  \n01:57 - Focus on Key Topics  \n02:00 - Transition to Pretraining  \n03:02 - Overview of Language Modeling  \n04:17 - Generative Models Explained  \n05:15 - Autoregressive Models Definition  \n06:36 - Autoregressive Task Explanation  \n07:49 - Training Overview  \n08:48 - Tokenization Importance  \n10:50 - Tokenization Process  \n13:30 - Example of Tokenization  \n16:00 - Evaluation with Perplexity  \n20:50 - Current Evaluation Methods  \n24:30 - Academic Benchmark: MMLU", "descriptionLinks": [{"url": "https://stanford.io/ai", "text": "https://stanford.io/ai"}, {"url": "https://yanndubs.github.io/", "text": "https://yanndubs.github.io/"}, {"url": "http://online.stanford.edu/", "text": "http://online.stanford.edu"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts", "text": "00:00"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=10s", "text": "00:10"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=16s", "text": "00:16"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=19s", "text": "00:19"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=76s", "text": "01:16"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=80s", "text": "01:20"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=93s", "text": "01:33"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=101s", "text": "01:41"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=107s", "text": "01:47"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=117s", "text": "01:57"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=120s", "text": "02:00"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=182s", "text": "03:02"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=257s", "text": "04:17"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=315s", "text": "05:15"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=396s", "text": "06:36"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=469s", "text": "07:49"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=528s", "text": "08:48"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=650s", "text": "10:50"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=810s", "text": "13:30"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=960s", "text": "16:00"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=1250s", "text": "20:50"}, {"url": "https://www.youtube.com/watch?v=9vM4p9NN0Ts&t=1470s", "text": "24:30"}], "subtitles": null, "order": 2, "commentsTurnedOff": false, "comments": null, "fromYTUrl": "https://www.youtube.com/results?search_query=machine+learning", "isMonetized": null, "hashtags": [], "formats": [], "isMembersOnly": false, "input": "machine learning"}]