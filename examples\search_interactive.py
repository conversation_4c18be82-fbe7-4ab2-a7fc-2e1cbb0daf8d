# search_interactive.py
import os
import sys
import argparse
from dotenv import load_dotenv
# Исправляем путь для импорта из src
PROJECT_ROOT_FOR_EXAMPLES = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, PROJECT_ROOT_FOR_EXAMPLES)

try:
    from src.scrapers.youtube_api.youtube_api import search_videos, get_channel_videos, get_channel_details
except ImportError:
    print("Ошибка: Не удалось импортировать функции из src.scrapers.youtube_api.youtube_api.")
    print("Убедитесь, что модуль существует и YOUTUBE_API_KEY настроен.")
    sys.exit(1)
    
from googleapiclient.errors import HttpError
import re
# urllib.parse не используется напрямую в видимой части, но может быть ниже
# from urllib.parse import urlparse, parse_qs # Оставим, если используется ниже

# Загружаем переменные окружения
load_dotenv(dotenv_path=os.path.join(PROJECT_ROOT_FOR_EXAMPLES, '.env')) # Указываем путь к .env

# Проверяем наличие API ключа
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")
if not YOUTUBE_API_KEY:
    print("ОШИБКА: API ключ YouTube (YOUTUBE_API_KEY) не найден в переменных окружения.")
    print(f"Пожалуйста, создайте файл .env в корне проекта ({PROJECT_ROOT_FOR_EXAMPLES}) с вашим YOUTUBE_API_KEY.")
    sys.exit(1)

def format_duration(duration_iso):
    """Преобразует длительность в формате ISO 8601 в читаемый формат."""
    if not duration_iso or not duration_iso.startswith('PT'):
        return 'Н/Д'
        
    duration = duration_iso[2:]  # Убираем 'PT'
    hours, minutes, seconds = 0, 0, 0
    
    if 'H' in duration:
        hours_str, duration = duration.split('H')
        hours = int(hours_str)
    
    if 'M' in duration:
        minutes_str, duration = duration.split('M')
        minutes = int(minutes_str)
    
    if 'S' in duration:
        seconds_str = duration.replace('S', '')
        seconds = int(seconds_str) if seconds_str.isdigit() else 0 # Добавлена проверка isdigit
    
    if hours > 0:
        return f"{hours}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes}:{seconds:02d}"

def format_count(count_str):
    """Форматирует числовое значение для лучшей читаемости."""
    if not count_str:
        return 'Н/Д'
        
    try: # Добавлена обработка ошибок на случай невалидного count_str
        count = int(count_str)
    except ValueError:
        return 'Н/Д'
    
    if count >= 1000000:
        return f"{count/1000000:.1f}M"
    elif count >= 1000:
        return f"{count/1000:.1f}K"
    else:
        return str(count)

def display_videos(videos):
    """Отображает список видео."""
    if not videos:
        print("Видео не найдены.")
        return
        
    for i, video in enumerate(videos, 1):
        print(f"\\n{i}. \\033[1m{video['title']}\\033[0m")
        print(f"   URL: {video['url']}")
        print(f"   Канал: {video['channel_title']}")
        print(f"   Опубликовано: {video['published_at'][:10]}")
        
        duration = format_duration(video.get('duration', ''))
        print(f"   Длительность: {duration}")
        
        if 'statistics' in video and video['statistics'] is not None: # Добавлена проверка на None
            views = format_count(video['statistics'].get('viewCount', '0'))
            likes = format_count(video['statistics'].get('likeCount', '0'))
            print(f"   Просмотры: {views}, Лайки: {likes}")
        else:
            print("   Статистика: Н/Д")


def extract_channel_id(input_str):
    """
    Извлекает ID канала из URL или возвращает входную строку, если это ID.
    """
    if input_str.startswith('UC') and len(input_str) == 24 and re.match(r'^UC[a-zA-Z0-9_-]{22}$', input_str):
        return input_str
        
    channel_patterns = [
        r'youtube\\.com/channel/(UC[a-zA-Z0-9_-]{22})',
        r'youtube\\.com/c/([^/\\?]+)',
        r'youtube\\.com/user/([^/\\?]+)',
        r'youtube\\.com/@([^/\\?]+)'
    ]
    
    for pattern in channel_patterns:
        match = re.search(pattern, input_str)
        if match:
            if 'channel/' in pattern: #  youtube.com/channel/UC...
                return match.group(1)
            else: # youtube.com/c/name, youtube.com/user/name, youtube.com/@name
                # Для этих форматов требуется дополнительный запрос для разрешения имени/псевдонима в ID
                # В текущей реализации youtube_api.py, get_channel_details может принимать имя пользователя/custom URL,
                # но лучше передавать ID, если он известен.
                # Здесь мы просто вернем извлеченное имя/псевдоним, а get_channel_details попытается его разрешить.
                # Или можно явно указать, что это не ID.
                # print(f"Обнаружен псевдоним/имя пользователя: {match.group(1)}. Попытка использовать его для поиска.")
                # return match.group(1) # Возвращаем имя, а API попробует его обработать
                # Либо, если мы хотим только ID:
                print(f"Обнаружен URL с именем/псевдонимом: {match.group(1)}.")
                print("Для прямого получения информации о канале используйте его ID (начинается с UC).")
                print("Попытка найти канал по этому имени/псевдониму может быть неточной или потребовать доп. шагов (не реализовано здесь).")
                return None # Явно указываем, что ID не извлечен
    
    print(f"Не удалось извлечь ID канала из '{input_str}'. Пожалуйста, используйте ID канала (UC...) или полный URL вида youtube.com/channel/UC...")
    return None

def download_video_via_main(url): # Переименовал функцию для ясности
    """
    Запускает src/main.py для обработки (скачивания, транскрибации) видео.
    """
    main_script_path = os.path.join(PROJECT_ROOT_FOR_EXAMPLES, "src", "main.py")
    
    print(f"\\nЗапуск обработки для видео: {url}")
    print(f"Вызов скрипта: {sys.executable} {main_script_path} {url}")
    
    # Рекомендация: Вместо os.system лучше использовать subprocess для лучшего контроля
    # import subprocess
    # process = subprocess.run([sys.executable, main_script_path, url], capture_output=True, text=True)
    # print("STDOUT:", process.stdout)
    # print("STDERR:", process.stderr)
    # if process.returncode == 0:
    # print("Обработка завершена успешно.")
    # else:
    # print("Ошибка во время обработки.")
    os.system(f"\"{sys.executable}\" \"{main_script_path}\" \"{url}\"")


def interactive_search():
    """Интерактивный поиск видео на YouTube."""
    try:
        print("\\n=== Интерактивный поиск видео на YouTube (через YouTube Data API) ===")
        print("Используется API KEY из .env файла.")
        print("Введите 'q' в любое время для выхода из текущего ввода или главного меню.")
        
        while True:
            print("\\nГлавное меню:")
            print("1. Поиск видео по запросу")
            print("2. Информация о канале (по ID или URL)")
            print("3. Список видео с канала (по ID или URL)")
            print("4. Обработать видео по URL (скачать/транскрибировать через src/main.py)")
            print("q. Выход из программы")
            
            choice = input("\\nВыберите опцию: ").strip().lower()
            
            if choice == 'q':
                print("Выход из программы.")
                break
            # Поиск видео
            elif choice == '1':
                query = input("Введите поисковый запрос: ").strip()
                if query == 'q': continue
                
                limit_str = input("Максимальное количество результатов [10]: ").strip()
                if limit_str == 'q': continue
                limit = int(limit_str) if limit_str.isdigit() and int(limit_str) > 0 else 10
                
                print("\\nПорядок сортировки:")
                order_options = {"1": "relevance", "2": "date", "3": "viewCount", "4": "rating"}
                for k, v in order_options.items(): print(f"{k}. {v.capitalize()}")
                
                order_choice = input(f"Выберите порядок [{list(order_options.keys())[0]} - {order_options['1']}]: ").strip()
                if order_choice == 'q': continue
                order = order_options.get(order_choice, "relevance")
                
                print(f"\\nИдет поиск по запросу: '{query}' (max: {limit}, order: {order})...")
                try:
                    results = search_videos(query=query, max_results=limit, order=order)
                    display_videos(results)
                    if results:
                        action_choice = input("\\nДействия: 1. Обработать видео из списка, 2. Главное меню [2]: ").strip()
                        if action_choice == '1':
                            vid_num_str = input(f"Введите номер видео для обработки (1-{len(results)}): ").strip()
                            if vid_num_str.isdigit():
                                vid_idx = int(vid_num_str) - 1
                                if 0 <= vid_idx < len(results):
                                    download_video_via_main(results[vid_idx]['url'])
                                else:
                                    print("Неверный номер видео.")
                            elif vid_num_str != 'q':
                                print("Неверный ввод.")
                except HttpError as e:
                    print(f"Ошибка API YouTube: {e}")
                except Exception as e:
                    print(f"Произошла ошибка: {e}")
            # Информация о канале
            elif choice == '2':
                channel_input = input("Введите ID канала (UC...) или полный URL канала: ").strip()
                if channel_input == 'q': continue
                
                channel_id = extract_channel_id(channel_input)
                if not channel_id:
                    # extract_channel_id уже выводит сообщение об ошибке
                    continue

                print(f"\\nЗапрос информации для канала ID: {channel_id}...")
                try:
                    channel_info = get_channel_details(channel_id)
                    if channel_info:
                        print(f"\\n\\033[1mКанал: {channel_info['title']}\\033[0m ({channel_info['id']})")
                        print(f"  URL: {channel_info['url']}")
                        stats = channel_info.get('statistics', {})
                        print(f"  Подписчики: {format_count(stats.get('subscriberCount', '0'))}")
                        print(f"  Просмотры: {format_count(stats.get('viewCount', '0'))}")
                        print(f"  Видео: {format_count(stats.get('videoCount', '0'))}")
                        if channel_info.get('description'):
                             print(f"  Описание: {channel_info['description'][:150]}...")
                    else:
                        print(f"Канал с ID '{channel_id}' не найден.")
                except HttpError as e:
                    print(f"Ошибка API YouTube: {e}")
                except Exception as e:
                    print(f"Произошла ошибка: {e}")
            # Видео канала
            elif choice == '3':
                channel_input = input("Введите ID канала (UC...) или полный URL канала для получения списка видео: ").strip()
                if channel_input == 'q': continue

                channel_id = extract_channel_id(channel_input)
                if not channel_id:
                    continue

                limit_str = input("Максимальное количество видео для отображения [10]: ").strip()
                if limit_str == 'q': continue
                limit = int(limit_str) if limit_str.isdigit() and int(limit_str) > 0 else 10
                
                print(f"\\nЗапрос видео для канала ID: {channel_id} (max: {limit})...")
                try:
                    # Сначала получим детали канала, чтобы убедиться, что он существует и получить название
                    channel_info = get_channel_details(channel_id) # Используем тот же ID
                    if not channel_info:
                        print(f"Канал с ID '{channel_id}' не найден.")
                        continue
                    
                    print(f"Получение видео для канала: {channel_info['title']}")
                    videos = get_channel_videos(channel_id=channel_id, max_results=limit) # Используем тот же ID
                    display_videos(videos)
                    if videos:
                        action_choice = input("\\nДействия: 1. Обработать видео из списка, 2. Главное меню [2]: ").strip()
                        if action_choice == '1':
                            vid_num_str = input(f"Введите номер видео для обработки (1-{len(videos)}): ").strip()
                            if vid_num_str.isdigit():
                                vid_idx = int(vid_num_str) - 1
                                if 0 <= vid_idx < len(videos):
                                    download_video_via_main(videos[vid_idx]['url'])
                                else:
                                    print("Неверный номер видео.")
                            elif vid_num_str != 'q':
                                 print("Неверный ввод.")
                except HttpError as e:
                    print(f"Ошибка API YouTube: {e}")
                except Exception as e:
                    print(f"Произошла ошибка: {e}")
            # Обработать видео по URL
            elif choice == '4':
                video_url = input("Введите URL видео YouTube для обработки: ").strip()
                if video_url == 'q': continue
                if "youtube.com/watch?v=" in video_url or "youtu.be/" in video_url:
                    download_video_via_main(video_url)
                else:
                    print("URL не похож на ссылку на видео YouTube.")
            
            else:
                print("Неверная опция, попробуйте еще раз.")

    except KeyboardInterrupt:
        print("\\nПрограмма прервана пользователем.")
    except Exception as e:
        print(f"\\nПроизошла непредвиденная ошибка в interactive_search: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # argparse здесь не используется, так как это интерактивный скрипт.
    # Если бы мы хотели передавать, например, API ключ через аргументы, то он бы понадобился.
    # parser = argparse.ArgumentParser(description="Интерактивный поиск и загрузка видео с YouTube.")
    # Можно добавить аргументы, если они нужны для инициализации
    # args = parser.parse_args()
    interactive_search() 