# YouTube Data Processor

## 1. Обзор проекта

Этот проект предназначен для автоматизации сбора данных с YouTube, их обработки (включая скачивание видео/аудио и транскрибацию) и сохранения результатов в базу данных PostgreSQL. Проект включает модули для скрапинга через Apify и YouTube Data API, загрузки контента и его последующей транскрибации.

## 2. Основная цель для AI-ассистента

AI-ассистент используется для помощи в разработке, рефакторинге, отладке и расширении функциональности этого проекта. Важно, чтобы AI понимал структуру проекта, назначение модулей и потоки данных для эффективного внесения изменений и предложения улучшений.

## 3. Ключевые технологии

*   **Язык программирования**: Python 3.x
*   **База данных**: PostgreSQL
*   **Скрапинг**:
    *   Apify (через API)
    *   YouTube Data API v3 (расширенная интеграция, подробная работа с метаданными)
*   **Загрузка видео/аудио**: yt-dlp (планируется, текущая реализация `VideoDownloader` использует заглушку, но уже интегрирована с YouTube API)
*   **Транскрибация**: Внешний сервис (например, Gemini API, текущая реализация `TranscriptionService` является заглушкой)
*   **Управление зависимостями**: `pip` и `requirements.txt`
*   **Переменные окружения**: `python-dotenv` для управления конфигурацией через `.env` файл.

## 4. Инструкции по установке и настройке

1.  **Клонируйте репозиторий**:
    ```bash
    git clone <repository_url>
    cd youtube_downloader
    ```

2.  **Создайте и активируйте виртуальное окружение** (рекомендуется):
    ```bash
    python -m venv venv
    # Windows
    venv\\Scripts\\activate
    # macOS/Linux
    source venv/bin/activate
    ```

3.  **Установите зависимости**:
    ```bash
    pip install -r requirements.txt
    ```

4.  **Настройте файл переменных окружения**:
    *   Скопируйте `.env.example` в `.env`:
        ```bash
        cp .env.example .env
        ```
    *   Откройте файл `.env` и заполните все необходимые значения:
        *   `DB_TYPE`: Тип базы данных (например, `postgresql`).
        *   `DB_USER`: Имя пользователя PostgreSQL.
        *   `DB_PASSWORD`: Пароль пользователя PostgreSQL.
        *   `DB_HOST`: Хост сервера PostgreSQL.
        *   `DB_PORT`: Порт сервера PostgreSQL.
        *   `DB_NAME`: Имя базы данных PostgreSQL.
        *   `APIFY_API_TOKEN` (опционально): Ваш API токен для Apify, если планируете использовать скрапер Apify.
        *   `YOUTUBE_API_KEY` (обязательно для YouTube API): Ваш API ключ для YouTube Data API v3.
        *   `GEMINI_API_KEY` (опционально): Ваш API ключ для сервиса транскрибации Gemini (или другого, если `TranscriptionService` будет адаптирован).
        *   Убедитесь, что указанные базы данных и пользователи существуют в PostgreSQL.

## 5. Структура проекта и описание модулей

### 5.1. Основная директория `src/`

Содержит основной исходный код проекта.

#### 5.1.1. `src/main.py`
*   **Путь**: `src/main.py`
*   **Назначение**: Главный оркестратор полного цикла обработки видео: загрузка аудиодорожки, транскрибация и сохранение результатов в БД `transcripts`.
*   **Ключевые компоненты**: Функция `main()`, использующая `VideoDownloader`, `YouTubeApiClient` и `TranscriptionService`.
*   **Входные данные**:
    *   `youtube_url` (обязательный): URL видео на YouTube (аргумент командной строки).
    *   `--cookies` (опциональный): Путь к файлу `cookies.txt` для `yt-dlp` (аргумент командной строки).
    *   `--use-api` (опциональный): Флаг для использования YouTube API для получения расширенных метаданных.
    *   `--youtube-api-key` (опциональный): API ключ для YouTube Data API (по умолчанию из .env файла).
    *   `--transcription_api_key` (опциональный): API ключ для сервиса транскрибации (аргумент командной строки или переменная окружения `GEMINI_API_KEY`).
*   **Выходные данные/Действия**:
    *   Создает папку `downloads/<video_id>/` для сохранения медиафайлов.
    *   Если указан флаг `--use-api`, получает подробные метаданные через YouTube API.
    *   Сохраняет загруженный аудиофайл (текущая реализация `VideoDownloader` использует заглушку).
    *   Получает транскрипцию аудио (текущая реализация `TranscriptionService` использует заглушку).
    *   Сохраняет информацию о видео, путь к аудио, транскрипцию и метаданные в таблицу `transcripts` PostgreSQL.
    *   Выводит лог выполнения в консоль.
*   **Как запустить/использовать**:
    ```bash
    # Базовое использование
    python src/main.py <youtube_url>
    
    # Использование с YouTube API для получения расширенных метаданных
    python src/main.py <youtube_url> --use-api
    
    # Использование с файлом cookies для доступа к закрытым видео
    python src/main.py <youtube_url> --cookies path/to/cookies.txt --use-api
    ```

#### 5.1.2. Модули скрапинга `src/scrapers/`

##### `src/scrapers/apify/youtube_apify.py`
*   **Путь**: `src/scrapers/apify/youtube_apify.py`
*   **Назначение**: Содержит логику для взаимодействия с Apify API для сбора данных с YouTube (поиск, информация о канале, плейлисты).
*   **Ключевые компоненты**:
    *   Класс `YouTubeScraper`: Основной класс для выполнения запросов к Apify. Методы для поиска, получения видео канала/плейлиста.
    *   Функция `save_results_db()`: Сохраняет полученные от Apify данные в таблицу `youtube_videos_pg` в PostgreSQL. Динамически создает/изменяет столбцы таблицы при необходимости.
*   **Входные данные**:
    *   Для `YouTubeScraper`: поисковые запросы, URL каналов/плейлистов, лимиты. Требует `APIFY_API_TOKEN` из `.env`.
    *   Для `save_results_db()`: список словарей с данными видео, тип ввода (`input_type`), исходный запрос/URL (`query_or_url`).
*   **Выходные данные/Действия**:
    *   `YouTubeScraper` возвращает списки словарей с данными видео.
    *   `save_results_db()` записывает данные в БД.
*   **Как запустить/использовать**: Используется скриптом `examples/youtube_search.py`.

##### `src/scrapers/youtube_api/youtube_api.py` и `src/scrapers/youtube_api/youtube_api_client.py`
*   **Путь**: 
    *   `src/scrapers/youtube_api/youtube_api.py` - базовые функции для работы с YouTube API
    *   `src/scrapers/youtube_api/youtube_api_client.py` - клиентский класс для комплексной работы с API
*   **Назначение**: Модули для взаимодействия с официальным YouTube Data API v3. Предоставляют функции для поиска видео, получения подробной информации о видео, каналах, комментариях, субтитрах и связанных видео.
*   **Ключевые компоненты**:
    *   Базовые функции: `search_videos()`, `get_channel_videos()`, `get_channel_details()`, `get_video_details()`, `get_video_comments()`, `get_video_categories()`, `get_video_captions()`
    *   Класс `YouTubeApiClient`: Интегрирует все базовые функции и добавляет дополнительные методы:
        *   `get_related_videos()` - получение связанных с видео рекомендаций
        *   `get_complete_video_data()` - получение полного набора информации о видео
        *   `get_metadata_for_download()` - подготовка метаданных для использования в VideoDownloader
*   **Входные данные**: Поисковые запросы, ID видео/каналов, параметры API. Требует `YOUTUBE_API_KEY` из `.env`.
*   **Выходные данные/Действия**: Возвращает подробные словари или списки словарей с информацией о видео/каналах/комментариях/субтитрах.
*   **Как запустить/использовать**: 
    *   Используется скриптами `examples/search_cli.py` и `examples/search_interactive.py`
    *   Интегрирован с `src/main.py` для получения подробных метаданных о видео
    *   Отдельное тестирование можно выполнить:
    ```bash
    python src/scrapers/youtube_api/youtube_api.py
    ```

После запуска скрипт предложит ввести поисковый запрос и затем настроить дополнительные параметры поиска в интерактивном режиме:

1.  **Введите поисковый запрос**: Основной текст для поиска видео (например, `AI news`).
2.  **Количество результатов**: Число видео, которое вы хотите получить (по умолчанию 5).
3.  **Порядок сортировки**:
    *   **По релевантности (relevance)** (по умолчанию): При выборе этой опции, поиск по умолчанию будет ограничен видео, опубликованными за **последние 4 месяца**. Вы сможете изменить начальную дату этого периода.
    *   По дате публикации (date) - все видео.
    *   По количеству просмотров (viewCount) - все видео.
    *   По рейтингу (rating) - все видео.
    *   По названию (title) - все видео.
4.  **Опубликовано после (YYYY-MM-DD)**: Дата, после которой видео были опубликованы. 
    *   Если выбрана сортировка "По релевантности", по умолчанию здесь будет предложена дата "4 месяца назад". Вы можете ввести свою дату или нажать Enter, чтобы использовать предложенную.
    *   Для других типов сортировки это поле по умолчанию пустое (без фильтра). Вы можете ввести дату для применения фильтра.
5.  **Опубликовано до (YYYY-MM-DD)**: Дата, до которой видео были опубликованы (например, `2023-12-31`). Оставьте пустым, если не хотите применять этот фильтр.

**Примечание**: Для интерактивного режима регион поиска по умолчанию установлен как `US`, а язык поиска - `en`.

После выполнения поиска скрипт отобразит найденные видео и предложит:

*   **Получить информацию о канале первого видео**: Если выбрать "да" (или нажать Enter), скрипт покажет детали канала (название, подписчики, количество просмотров и видео).
*   **Получить видео с этого канала**: Если выбрать "да" (или нажать Enter), скрипт запросит количество видео для загрузки (по умолчанию 3) и отобразит их.

Все найденные и запрошенные видео будут автоматически сохранены в базу данных PostgreSQL, если она настроена корректно.

### Программное использование (импорт в другой скрипт)

Модуль `youtube_api.py` также можно импортировать в другие Python-скрипты для использования его функций напрямую.

```python
from src.scrapers.youtube_api.youtube_api import search_videos, get_video_details, get_channel_videos, get_channel_details

# Пример поиска видео
results = search_videos(
    query="Python tutorials",
    max_results=10,
    order="viewCount",
    region_code="US",
    relevance_language="en",
    published_after="2023-01-01T00:00:00Z",
    published_before="2023-12-31T23:59:59Z"
)

for video in results:
    print(video['title'])

# Пример получения деталей видео
video_details = get_video_details("VIDEO_ID_HERE")
if video_details:
    print(video_details['title'], video_details['view_count'])

# Пример получения видео с канала
channel_videos_list = get_channel_videos(channel_id="CHANNEL_ID_HERE", max_results=5)
for video in channel_videos_list:
    print(video['title'])
```

Убедитесь, что переменные окружения для API ключа YouTube и подключения к базе данных настроены перед использованием функций модуля.

#### 5.1.3. Модуль загрузки видео `src/downloader/`

##### `src/downloader/video_downloader.py`
*   **Путь**: `src/downloader/video_downloader.py`
*   **Назначение**: Класс `VideoDownloader` для загрузки аудиодорожек видео с YouTube с помощью `yt-dlp`. Скачивает аудио в оригинальном формате (обычно `.webm` или `.m4a`).
*   **Ключевые возможности**:
    *   Поддержка как ID видео, так и полных URL YouTube.
    *   Получение метаданных видео (название, канал, дата публикации и т.д.).
    *   Сохранение обложки видео (если доступна).
    *   Опциональное использование YouTube API для расширенных метаданных.
    *   Поддержка приватных/защищенных видео через файл cookies.

*   **Основное использование**:

    ```python
    # Базовое использование (ID или URL)
    from src.downloader.video_downloader import VideoDownloader
    
    downloader = VideoDownloader()
    video_id = "dQw4w9WgXcQ"  # или полный URL
    output_dir = f"downloads/{video_id}"
    
    audio_path, metadata = downloader.download_video(video_id, output_dir)
    print(f"Скачано: {audio_path}")
    ```

*   **Запуск из командной строки**:
    
    Модуль интегрирован с основным скриптом `src/main.py`:
    ```bash
    # Базовое использование
    python src/main.py <video_id_or_url>
    
    # С YouTube API и cookies
    python src/main.py <video_id_or_url> --use-api --cookies path/to/cookies.txt
    ```
    
    Также создан отдельный скрипт `download_youtube_audio.py` для прямого скачивания аудио без транскрибации:
    ```bash
    # Базовое использование
    python download_youtube_audio.py <video_id_or_url>
    
    # С указанием выходной директории
    python download_youtube_audio.py <video_id_or_url> -o "my_folder"
    
    # С YouTube API
    python download_youtube_audio.py <video_id_or_url> --use-api
    
    # Использовать ffmpeg (по умолчанию отключено)
    python download_youtube_audio.py <video_id_or_url> --use-ffmpeg
    ```

#### 5.1.4. Модули транскрибации `src/transcriber/`

Модуль транскрибации был переработан для обеспечения большей гибкости и расширяемости. Теперь он включает центральный сервис (`TranscriptionService`) и систему провайдеров, позволяющую легко интегрировать и выбирать различные API для преобразования речи в текст.

#### Архитектура и компоненты:

*   **`TranscriptionService` (`src/transcriber/transcription_service.py`)**:
    *   **Роль**: Центральный оркестратор для выполнения задач транскрибации.
    *   **Принцип работы**: Принимает путь к аудиофайлу, имя желаемого провайдера транскрибации (например, "gemini"), API-ключ (опционально, может браться из переменных окружения) и специфичные для провайдера опции.
    *   **Автоматическая конвертация**: Если на вход подан файл `.webm`, сервис попытается сконвертировать его в `.mp3` с помощью `imageio-ffmpeg` (требуется установленная библиотека и доступный `ffmpeg`). Сконвертированный `.mp3` файл сохраняется в той же директории, что и исходный `.webm`, и не удаляется после использования.
    *   **Выбор провайдера**: Динамически загружает и использует указанный провайдер транскрибации.
    *   **Результат**: Возвращает объект `TranscriptionResult`, содержащий текст транскрипции, информацию о токенах (если доступно от провайдера), статус успеха и возможное сообщение об ошибке.
    *   **Использование**: Предназначен для программного вызова из других частей приложения (например, из `src/main.py`) для интеграции транскрибации в основной рабочий процесс. Вызывающий код отвечает за дальнейшую обработку результата, например, сохранение в базу данных.

*   **Система провайдеров**:
    *   **`ITranscriptionProvider` (в `src/transcriber/base_provider.py`)**: Абстрактный базовый класс (интерфейс), который определяет общие методы (`transcribe()`, `get_provider_name()`), которым должны следовать все конкретные реализации провайдеров. Это обеспечивает единообразие и позволяет легко добавлять поддержку новых сервисов транскрибации.
    *   **`TranscriptionResult` (в `src/transcriber/base_provider.py`)**: Класс данных (dataclass) для стандартизированного представления результатов операции транскрибации.
    *   **Конкретные провайдеры (в `src/transcriber/providers/`)**: Директория содержит реализации интерфейса `ITranscriptionProvider` для различных сервисов:
        *   **`GeminiProvider` (`src/transcriber/providers/gemini_provider.py`)**: Реализация для работы с Google Gemini API. Инкапсулирует логику вызова API, обработку промптов, настройку языка, запрос таймкодов и получение информации об использованных токенах.
        *   *(В будущем здесь могут появиться `OpenAIProvider`, `WhisperProvider` и т.д., реализующие тот же интерфейс `ITranscriptionProvider`)*

*   **`transcription_db.py` (`src/transcriber/transcription_db.py`)**:
    *   (Описание из предыдущей версии `README.md` остается актуальным). Модуль отвечает за взаимодействие с таблицей `transcripts` в базе данных PostgreSQL. `TranscriptionService` сам по себе не сохраняет данные в БД; это задача вызывающего кода (например, `src/main.py`) после получения успешного `TranscriptionResult`.

#### Программное использование `TranscriptionService` (Пример):

```python
# В вашем скрипте, например, в src/main.py
from src.transcriber import TranscriptionService, TranscriptionResult
from rich.console import Console
import os

# Инициализация сервиса (опционально с выводом в консоль)
console = Console()
service = TranscriptionService(console=console)

audio_file = "путь/к/вашему/файлу.webm" # или .mp3, .wav и т.д.
provider_to_use = "gemini"
api_key_for_provider = os.getenv("GEMINI_API_KEY") # Или передать строку с ключом

options_for_provider = {
    "language": "ru",
    "with_timestamps": True,
    "prompt_text": "Транскрибируй эту аудиозапись.",
    # "model_name": "gemini-1.5-pro" # Пример дополнительного специфичного параметра для GeminiProvider
}

transcription_result: TranscriptionResult = service.transcribe(
    audio_file_path=audio_file,
    provider_name=provider_to_use,
    api_key=api_key_for_provider, # Может быть None, если ключ в переменной окружения
    provider_options=options_for_provider
)

if transcription_result.success:
    print(f"Транскрипция ({provider_to_use}):\n{transcription_result.text}")
    if transcription_result.token_info:
        print(f"Токены: {transcription_result.token_info}")
    # Здесь код для сохранения transcription_result.text и другой информации
    # в базу данных с помощью модуля transcription_db.py
else:
    print(f"Ошибка транскрибации: {transcription_result.error_message}")
```

#### Отдельный CLI-инструмент: `src/transcriber/Gemini/gemini_transcriber.py`

Скрипт `src/transcriber/Gemini/gemini_transcriber.py` представляет собой **независимый инструмент командной строки**, предназначенный для быстрой транскрибации аудиофайлов напрямую через Google Gemini API и сохранения результата в локальный `.txt` файл.

*   **Независимость**: Он **не использует** новый `TranscriptionService` или систему провайдеров, описанную выше. Вместо этого он напрямую работает с библиотеками `google-generativeai` и `imageio-ffmpeg`.
*   **Основные возможности**:
    *   Принимает на вход ID видео (поиск аудио в папке `downloads/`) или прямой путь к аудиофайлу.
    *   Автоматическая конвертация `.webm` в `.mp3` (сконвертированный `.mp3` сохраняется), если `ffmpeg` доступен.
    *   Настройка языка транскрибации (по умолчанию **русский** - `ru`).
    *   Использование пользовательских промптов (из файла или стандартного).
    *   Запрос и отображение таймкодов.
    *   Сохранение результата (включая информацию о токенах) в текстовый файл в той же директории, где находится обработанный аудиофайл. Имя файла формируется как `ГГ-ММ-ДД_ЧЧ-ММ_имя_файла.txt` или `ГГ-ММ-ДД_ЧЧ-ММ_videoID.txt`.
*   **Параметры командной строки**:
    *   `target_input`: (Обязательный) ID видео или путь к аудиофайлу.
    *   `-o, --output-name`: Базовое имя выходного файла (по умолчанию 'транскрипт' или имя исходного файла/videoID).
    *   `-t, --timestamps`: Запросить таймкоды.
    *   `-p, --prompt`: Использовать промпт из файла (`prompt.txt` или `transcript_prompt.txt`).
    *   `-f, --prompt-file`: Указать конкретный файл с промптом.
    *   `-l, --language`: Язык транскрибации (например, `en`, `de`). По умолчанию `ru`.
*   **Запуск**:
    ```bash
    # Пример: транскрибировать аудио для видео ID 'F8NKVhkZZWI' 
    # (язык по умолчанию - русский, результат сохранится в папке с аудио)
    python src/transcriber/Gemini/gemini_transcriber.py F8NKVhkZZWI

    # Указать другой язык (английский) и запросить таймкоды
    python src/transcriber/Gemini/gemini_transcriber.py F8NKVhkZZWI -l en -t

    # Использовать свой файл промпта
    python src/transcriber/Gemini/gemini_transcriber.py path/to/your/audio.mp3 -f path/to/myprompt.txt

    # Задать базовое имя для выходного файла
    python src/transcriber/Gemini/gemini_transcriber.py F8NKVhkZZWI -o my_video_transcript
    ```

Таким образом, проект предоставляет два способа использования транскрибации Gemini: гибкий программный интерфейс через `TranscriptionService` для интеграции в основной пайплайн приложения, и удобный CLI-инструмент `gemini_transcriber.py` для отдельных задач.

#### 5.1.5. Утилиты для БД `src/db_utils/`

##### `src/db_utils/db_connector.py`
*   **Путь**: `src/db_utils/db_connector.py`
*   **Назначение**: Предоставляет унифицированную функцию для подключения к базе данных PostgreSQL.
*   **Ключевые компоненты**: Функция `get_db_connection()`.
*   **Входные данные**: Считывает параметры подключения к БД из файла `.env`.
*   **Выходные данные/Действия**: Возвращает объект соединения `psycopg2`.
*   **Как запустить/использовать**: Импортируется и используется всеми модулями, которым требуется доступ к БД.

##### `src/db_utils/pg_test_connection.py`
*   **Путь**: `src/db_utils/pg_test_connection.py`
*   **Назначение**: Скрипт для тестирования соединения с PostgreSQL и выполнения базовых CRUD-операций на тестовой таблице.
*   **Как запустить/использовать**:
    ```bash
    python src/db_utils/pg_test_connection.py
    ```
    Также может вызываться другими скриптами (например, `examples/youtube_search.py`) для проверки доступности БД перед началом работы.
*   **Выходные данные/Действия**: Выводит сообщения в консоль о результате теста соединения и операций.

### 5.2. Примеры использования `examples/`

Директория содержит скрипты, демонстрирующие использование различных модулей проекта.

#### `examples/youtube_search.py`
*   **Путь**: `examples/youtube_search.py`
*   **Назначение**: CLI-инструмент для скрапинга данных с YouTube с использованием Apify (через `YouTubeScraper`) и сохранения результатов в таблицу `youtube_videos_pg`.
*   **Входные данные**: Тип поиска (`search`, `channel`, `playlist`), поисковый запрос или URL, лимит результатов (аргументы командной строки).
*   **Как запустить/использовать**:
    ```bash
    python examples/youtube_search.py search "Python tutorials" --limit 5
    python examples/youtube_search.py channel <channel_url_or_id> --limit 10
    ```
*   **Выходные данные/Действия**: Сохраняет данные в БД `youtube_videos_pg`, выводит лог в консоль. Перед запуском выполняет тест соединения с БД (`pg_test_connection.py`).

#### `examples/search_cli.py`
*   **Путь**: `examples/search_cli.py`
*   **Назначение**: CLI-инструмент для поиска на YouTube через YouTube Data API. Позволяет искать видео, получать информацию о каналах и видео, а также инициировать их обработку через `src/main.py`.
*   **Входные данные**: Команды (`search`, `channel`, `video`), поисковые запросы, ID каналов/видео, опции (аргументы командной строки).
*   **Как запустить/использовать**:
    ```bash
    # Поиск видео
    python examples/search_cli.py search "AI news" --limit 5 --download
    
    # Получение информации о канале и его видео
    python examples/search_cli.py channel <channel_id> --videos --limit 3
    
    # Получение подробной информации о видео
    python examples/search_cli.py video <video_id_or_url> 
    
    # Получение полной информации включая комментарии и связанные видео
    python examples/search_cli.py video <video_id_or_url> --full
    
    # Получение отдельных компонентов информации
    python examples/search_cli.py video <video_id_or_url> --comments --related --subtitles
    ```
*   **Выходные данные/Действия**: 
    * Выводит результаты поиска, информацию о канале или подробную информацию о видео в консоль
    * Для видео может показывать комментарии, субтитры и связанные видео
    * При указании опции `--download` (или `-d`) предлагает выбрать видео и запускает `src/main.py` для выбранного URL с флагом `--use-api` для использования YouTube API

#### `examples/search_interactive.py`
*   **Путь**: `examples/search_interactive.py`
*   **Назначение**: Интерактивный CLI-инструмент для работы с YouTube Data API. Предоставляет меню для поиска видео, получения информации о каналах и запуска обработки видео через `src/main.py`.
*   **Входные данные**: Ввод пользователя через интерактивное меню.
*   **Как запустить/использовать**:
    ```bash
    python examples/search_interactive.py
    ```
*   **Выходные данные/Действия**: Отображает информацию в консоли в интерактивном режиме. Может запускать `src/main.py` для обработки выбранных видео.

### 5.3. Конфигурационные файлы

#### `config.py`
*   **Путь**: `config.py` (в корне проекта)
*   **Назначение**: Хранение неконфиденциальных настроек проекта (например, путь по умолчанию к файлу cookies `COOKIES_FILE_PATH`).
*   **Как запустить/использовать**: Импортируется другими модулями при необходимости.

#### `.env` / `.env.example`
*   **Путь**: `.env` (в корне проекта, создается пользователем), `.env.example` (в корне проекта, шаблон)
*   **Назначение**: Хранение конфиденциальных данных и настроек окружения (ключи API, учетные данные БД). `.env` находится в `.gitignore` и не должен попадать в репозиторий.
*   **Как запустить/использовать**: Загружается модулем `dotenv` при старте скриптов, использующих эти переменные.

### 5.4. `requirements.txt`
*   **Путь**: `requirements.txt` (в корне проекта)
*   **Назначение**: Содержит список всех Python-зависимостей проекта с их версиями.
*   **Ключевые зависимости**:
    *   `apify-client`: Для взаимодействия с Apify API.
    *   `python-dotenv`: Для работы с переменными окружения.
    *   `google-api-python-client`: Для взаимодействия с YouTube Data API.
    *   `google-auth`, `google-auth-httplib2`, `google-auth-oauthlib`: Для авторизации в API Google.
    *   `psycopg2-binary`: Драйвер PostgreSQL.
    *   `yt-dlp`: Для загрузки видео и аудио с YouTube (планируется использование).
*   **Как запустить/использовать**: Используется для установки зависимостей: `pip install -r requirements.txt`.

## 6. Используемые базы данных (PostgreSQL)

### 6.1. Таблица `youtube_videos_pg`
*   **Назначение**: Хранит метаданные видео, полученные с помощью скрапера Apify.
*   **Ключевые столбцы**:
    *   `id` (SERIAL PRIMARY KEY): Уникальный идентификатор записи.
    *   `video_id` (TEXT UNIQUE): Уникальный идентификатор видео на YouTube.
    *   `input_type` (TEXT): Тип источника данных (например, `search`, `channel`, `playlist`).
    *   `query_or_url` (TEXT): Исходный поисковый запрос или URL, использованный для получения данных.
*   **Динамические столбцы**: Остальные столбцы создаются и заполняются динамически на основе данных, возвращаемых Apify (например, `title`, `url`, `description`, `thumbnail_url`, `view_count`, `like_count`, `channel_name`, `channel_url`, `published_at`, `duration_formatted`, etc.). Типы данных для динамических столбцов преимущественно `TEXT`, сложные структуры (списки, словари) сериализуются в JSON-строки.

### 6.2. Таблица `transcripts`
*   **Назначение**: Хранит информацию о загруженных видео, их транскрипциях и статусах обработки.
*   **Ключевые столбцы (определяются в `src/transcriber/transcription_db.py::initialize_transcripts_table`)**:
    *   `video_id` (TEXT PRIMARY KEY): Уникальный идентификатор видео на YouTube.
    *   `title` (TEXT): Название видео.
    *   `original_url` (TEXT): Оригинальный URL видео.
    *   `upload_date_str` (TEXT): Дата загрузки видео (в формате YYYYMMDD).
    *   `duration_seconds` (INTEGER): Длительность видео в секундах.
    *   `channel_name` (TEXT): Название канала.
    *   `channel_url` (TEXT): URL канала.
    *   `audio_file_path` (TEXT): Локальный путь к загруженному аудиофайлу.
    *   `video_data_folder` (TEXT): Локальный путь к папке с данными видео (включая аудио).
    *   `transcription_text` (TEXT): Полный текст транскрипции.
    *   `transcription_prompt_text` (TEXT): Текст промпта, использованного для транскрипции.
    *   `full_metadata` (JSONB): Полные метаданные видео в формате JSON.
    *   `status` (TEXT): Текущий статус обработки (например, `pending_download`, `downloaded`, `transcribing`, `transcribed`, `error_downloading`, `error_transcribing`).
    *   `error_message` (TEXT, nullable): Сообщение об ошибке, если она произошла.
    *   `created_at` (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP): Время создания записи.
    *   `updated_at` (TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP): Время последнего обновления записи.

### 6.3. Таблица `youtube_videos_api`
*   **Назначение**: Хранит метаданные видео, полученные с помощью официального YouTube Data API.
*   **Ключевые столбцы**:
    *   `id` (INTEGER PRIMARY KEY): Уникальный идентификатор записи.
    *   `video_id` (CHARACTER VARYING): Уникальный идентификатор видео на YouTube.
    *   `data` (JSONB): Полный JSON-объект с метаданными, полученный от YouTube API.
    *   `retrieved_at` (TIMESTAMP WITH TIME ZONE): Время получения данных.
    *   `created_at` (TIMESTAMP WITH TIME ZONE): Время создания записи.
    *   `updated_at` (TIMESTAMP WITH TIME ZONE): Время последнего обновления записи.

### 6.4. Таблица `youtube_videos_pg_test`
*   **Назначение**: Тестовая таблица для проверки подключения к базе данных и выполнения CRUD-операций.
*   **Ключевые столбцы**:
    *   `id` (INTEGER PRIMARY KEY): Уникальный идентификатор записи.
    *   `timestamp` (TIMESTAMP WITHOUT TIME ZONE): Время создания записи.
    *   `test_data` (TEXT): Тестовые данные.
    *   `input_type` (TEXT): Тип ввода/источника данных.

### 6.5. Текущее состояние базы данных
На момент анализа база данных содержит следующие данные:
* В таблице `youtube_videos_pg` имеются записи по видео с темой "ai agents" (более 28 записей)
* В таблице `youtube_videos_api` хранятся JSON-объекты с подробной информацией о 5 видео
* В таблице `transcripts` имеется одна запись с транскрипцией
* Таблица `youtube_videos_pg_test` пуста, но готова для тестирования

Значительная часть видео в базе данных посвящена AI-агентам и автоматизации процессов, что соответствует тематике проекта и может быть использовано для дальнейшего анализа и построения обучающих моделей.

## 7. Высокоуровневая схема потока данных

1.  **Сбор исходных URL/ID видео и метаданных**:
    *   **Вариант А (Apify)**: Запуск `examples/youtube_search.py` -> `YouTubeScraper` собирает данные -> `save_results_db` сохраняет в `youtube_videos_pg`.
    *   **Вариант Б (YouTube Data API - базовый)**: Запуск `examples/search_cli.py` или `examples/search_interactive.py` -> функции из `src/scrapers/youtube_api/youtube_api.py` получают данные -> пользователь выбирает видео -> URL передается в `src/main.py`.
    *   **Вариант В (YouTube Data API - расширенный)**: Запуск `examples/search_cli.py video <video_id>` -> `YouTubeApiClient.get_complete_video_data()` получает подробную информацию о видео (комментарии, субтитры, связанные видео) -> пользователь может загрузить видео -> вызов `src/main.py` с флагом `--use-api`.
2.  **Основная обработка (через `src/main.py`)**:
    *   Вход: URL видео, опционально флаг `--use-api` для получения расширенных метаданных.
    *   При указании `--use-api`: Инициализирует `YouTubeApiClient` и передает его в `VideoDownloader`.
    *   `VideoDownloader`: Загружает аудио (цель: `downloads/<video_id>/<audio_file>`) и получает метаданные (через YouTube API, если указан флаг `--use-api`).
    *   `TranscriptionService`: Отправляет аудио на транскрибацию, получает текст.
    *   `transcription_db.py`: Сохраняет все данные (метаданные, пути, транскрипцию, статус) в таблицу `transcripts`.

## 8. Рекомендации для AI-ассистента

*   **Модульность**: При добавлении новой функциональности старайтесь определить наиболее подходящий существующий модуль или создайте новый, следуя принятой структуре.
*   **База данных**:
    *   Для общих операций подключения к БД используйте `get_db_connection()` из `src.db_utils.db_connector`.
    *   Логика работы с конкретными таблицами вынесена в специализированные модули (например, `src.transcriber.transcription_db.py` для таблицы `transcripts`, `src.scrapers.apify.youtube_apify.py` для `youtube_videos_pg`).
    *   При работе с существующими данными учитывайте, что таблица `youtube_videos_pg` содержит множество столбцов, некоторые из которых могут быть NULL. Используйте гибкие запросы с проверкой наличия данных.
    *   При создании новых таблиц следуйте существующим конвенциям: первичные ключи как `id SERIAL PRIMARY KEY`, внешние ключи именуются по схеме `entity_id`, использование типа JSONB для сложных структур данных.
    *   Для сохранения JSON-данных из YouTube API рекомендуется использовать таблицу `youtube_videos_api`, для данных из скрапера - `youtube_videos_pg`.
*   **Конфигурация**: API ключи и учетные данные БД должны храниться **только** в файле `.env` и загружаться через `os.getenv()`. Неконфиденциальные настройки могут находиться в `config.py`.
*   **Обработка ошибок**: Обеспечивайте адекватную обработку ошибок и логирование, особенно при взаимодействии с внешними API и базой данных.
*   **Заглушки**: Модули `VideoDownloader` и `TranscriptionService` в настоящее время содержат заглушки. При их доработке ориентируйтесь на их предполагаемое назначение.
*   **Тестирование**: Помните о необходимости тестирования нового кода. `src/db_utils/pg_test_connection.py` является примером простого теста.
*   **Документация**: Поддерживайте актуальность этого `README.md` и добавляйте комментарии к коду там, где это необходимо для понимания сложных участков.
*   **Работа с данными YouTube**: Учитывайте, что проект уже содержит информацию о видео по тематике AI-агентов, что может быть полезно для разработки и тестирования новых функций.

## 9. Перспективы развития проекта

### 9.1. Интеграция с моделями машинного обучения
Собранные данные (метаданные видео и транскрипции) могут служить основой для обучения моделей машинного обучения, например:
* Анализ тематики видео на основе заголовков, описаний и транскрипций
* Классификация видео по категориям
* Извлечение ключевых фраз и концепций из транскрипций
* Создание резюме или саммари контента

### 9.2. Анализ трендов и популярности
На основе собранных данных можно проводить анализ:
* Какие темы, связанные с AI-агентами, наиболее популярны (по просмотрам, лайкам)
* Какие каналы наиболее авторитетны в этой области
* Как меняются тренды во времени (требует регулярного сбора данных)

### 9.3. Создание специализированной базы знаний
Транскрипции видео могут быть преобразованы в структурированную базу знаний:
* Индексация и поиск по содержимому
* Связывание концепций между разными видео
* Создание глоссария терминов и их объяснений

### 9.4. Веб-интерфейс для доступа к данным
Разработка веб-приложения для удобного доступа к собранной информации:
* Поиск видео по ключевым словам в заголовках и транскрипциях
* Фильтрация по каналам, дате, популярности
* Визуализация связей между видео и концепциями
