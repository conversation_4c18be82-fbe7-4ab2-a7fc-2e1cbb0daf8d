
from apify_client import ApifyClient
import json

# Простой скрипт для тестирования API
print("Скрипт запущен")

try:
    # Инициализация клиента
    client = ApifyClient(token='**********************************************')
    print("Клиент инициализирован")

    # Получаем список пользовательских задач
    print("Получение списка задач...")
    tasks = client.tasks().list()
    print(f"Список задач получен: {json.dumps(tasks, indent=2)}")

    print("Скрипт завершен успешно")
except Exception as e:
    print(f"Произошла ошибка: {str(e)}")
