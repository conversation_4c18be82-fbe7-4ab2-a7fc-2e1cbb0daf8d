# main.py
import os
import argparse
from urllib.parse import urlparse, parse_qs
import sys
import subprocess # Добавлено для вызова yt-dlp
import json     # Добавлено для обработки вывода yt-dlp
from datetime import datetime # Добавлено для работы с датой и временем

# Добавляем корневую директорию проекта в sys.path, чтобы можно было импортировать config.py из корня
# Это нужно, если main.py запускается как python src/main.py
# Если запускать как python -m src.main из корня, то config можно импортировать напрямую
PROJECT_ROOT_FOR_CONFIG = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if PROJECT_ROOT_FOR_CONFIG not in sys.path:
    sys.path.insert(0, PROJECT_ROOT_FOR_CONFIG)

try:
    import config # Для доступа к COOKIES_FILE_PATH
except ImportError:
    print("Предупреждение: Не удалось импортировать config.py. Убедитесь, что он в корне проекта или настроен PYTHONPATH.")
    # Создадим заглушку, если config не найден, чтобы скрипт не падал сразу
    class ConfigMock:
        COOKIES_FILE_PATH = None
    config = ConfigMock()

# Импорты из наших модулей в src
from src.downloader import VideoDownloader
from src.transcriber import (
    TranscriptionService, 
    initialize_transcripts_table, 
    save_transcription_data, 
    update_transcription_status
)
# TRANSCRIPTION_PROMPT теперь должен быть частью TranscriptionService или его конфигурации

# Импортируем функции из модуля youtube_api (если они понадобятся)
try:
    from src.scrapers.youtube_api import YouTubeApiClient
    YOUTUBE_API_AVAILABLE = True
except (ImportError, ModuleNotFoundError) as e:
    print(f"Предупреждение: Не удалось импортировать YouTube API: {e}")
    YOUTUBE_API_AVAILABLE = False

# Определяем корневую директорию проекта (где находится папка src)
PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__))) # E:/Coding/ai/aiAgents/youtube_downloader

def get_youtube_video_id(url):
    """Извлекает ID видео из URL YouTube."""
    # Примеры URL:
    # http://www.youtube.com/watch?v=xxxxxxxxxxx
    # http://youtu.be/xxxxxxxxxxx
    # https://youtube.com/embed/xxxxxxxxxxx
    # https://m.youtube.com/watch?v=xxxxxxxxxxx
    parsed_url = urlparse(url)
    if parsed_url.hostname == 'youtu.be':
        return parsed_url.path[1:]
    if parsed_url.hostname in ('www.youtube.com', 'youtube.com', 'm.youtube.com'):
        if parsed_url.path == '/watch':
            p = parse_qs(parsed_url.query)
            return p.get('v', [None])[0]
        if parsed_url.path.startswith('/embed/'):
            return parsed_url.path.split('/embed/')[1].split('?')[0]
        if parsed_url.path.startswith('/v/'): # очень старый формат
            return parsed_url.path.split('/v/')[1].split('?')[0]
    return None

def main():
    parser = argparse.ArgumentParser(description="Полный цикл: Загрузка аудио с YouTube, транскрибация и сохранение в БД.")
    parser.add_argument("youtube_url", help="URL видео на YouTube для обработки.")
    parser.add_argument("--cookies", help="Путь к файлу cookies.txt для yt-dlp.", default=None)
    # Временно закомментируем параметры API, пока не исправим все
    # parser.add_argument("--use-api", help="Использовать YouTube API для получения метаданных.", action="store_true")
    # parser.add_argument("--youtube-api-key", help="API ключ для YouTube Data API.", default=os.getenv('YOUTUBE_API_KEY'))
    parser.add_argument("--transcription_api_key", help="API ключ для сервиса транскрибации.", default=os.getenv('GEMINI_API_KEY'))
    parser.add_argument("--disable-ffmpeg", help="Отключить использование FFmpeg для конвертации аудио", action="store_true")
    parser.add_argument("--prompt", help="Промпт для транскрибации (текст)", default=None)
    parser.add_argument("--prompt-file", help="Путь к файлу с промптом для транскрибации", default=None)
    parser.add_argument("--timestamps", help="Запрашивать таймкоды в транскрибации", action="store_true")

    args = parser.parse_args()
    video_url = args.youtube_url
    
    # Инициализируем клиент YouTube API, если пользователь указал --use-api и API доступен
    youtube_api_client = None
    # if args.use_api:
    #     if YOUTUBE_API_AVAILABLE:
    #         try:
    #             youtube_api_client = YouTubeApiClient(api_key=args.youtube_api_key)
    #             print("YouTube API клиент успешно инициализирован.")
    #         except Exception as e:
    #             print(f"Ошибка при инициализации YouTube API клиента: {e}")
    #             print("Будет использован метод без API (только загрузка видео).")
    #     else:
    #         print("YouTube API недоступен. Убедитесь, что библиотека google-api-python-client установлена.")
    #         print("Будет использован метод без API (только загрузка видео).")

    # --- Инициализация сервисов ---
    # downloader = VideoDownloader(api_client=youtube_api_client)
    downloader = VideoDownloader(use_ffmpeg=not args.disable_ffmpeg)
    
    # Сообщение о состоянии FFmpeg
    if args.disable_ffmpeg:
        print("FFmpeg для конвертации аудио отключен. Файлы будут сохранены в исходном формате.")
    
    # API ключ для транскрибации может браться из аргумента или переменных окружения
    transcription_service = TranscriptionService(api_key=args.transcription_api_key)

    # Определяем путь к файлу cookies
    cookies_path_arg = args.cookies
    actual_cookies_file = None
    if cookies_path_arg:
        if os.path.isabs(cookies_path_arg) or cookies_path_arg.startswith(('.', './', '.\\\\')):
            actual_cookies_file = cookies_path_arg
        else:
            actual_cookies_file = os.path.join(PROJECT_ROOT, cookies_path_arg)
    elif hasattr(config, 'COOKIES_FILE_PATH') and config.COOKIES_FILE_PATH:
        actual_cookies_file = os.path.join(PROJECT_ROOT, config.COOKIES_FILE_PATH)

    if actual_cookies_file and not os.path.exists(actual_cookies_file):
        print(f"Предупреждение: Файл cookies '{actual_cookies_file}' не найден. Загрузка будет без аутентификации.")
        actual_cookies_file = None
    elif actual_cookies_file:
        print(f"Используется файл cookies: {actual_cookies_file}")

    # Инициализация таблицы для транскрипций
    try:
        if not initialize_transcripts_table(): # Используем новую функцию
            print(f"Критическая ошибка: Не удалось инициализировать таблицу 'transcripts' в базе данных.")
            return 
    except Exception as e:
        print(f"Критическая ошибка: Не удалось инициализировать базу данных (таблица transcripts): {e}")
        return

    video_id = get_youtube_video_id(video_url)
    if not video_id:
        print(f"Не удалось извлечь ID видео из URL: {video_url}")
        # Можно добавить логику для записи ошибки в специальную таблицу "неудачных URL"
        return

    # --- Этап 1: Подготовка аудиофайла и метаданных ---
    audio_file_path = None
    metadata = None
    
    video_data_folder_name = video_id # Используем ID видео как имя папки по умолчанию
    download_base_path = os.path.join(PROJECT_ROOT, "downloads") 
    os.makedirs(download_base_path, exist_ok=True)
    specific_video_output_path = os.path.join(download_base_path, video_data_folder_name)
    os.makedirs(specific_video_output_path, exist_ok=True)
    video_data_folder = specific_video_output_path # Это всегда так для данного скрипта

    # Поиск существующего аудиофайла
    known_audio_extensions = ('.mp3', '.webm', '.m4a', '.opus', '.wav', '.ogg')
    preferred_order = ['.mp3', '.webm', '.m4a'] # Приоритет этим форматам
    candidate_audio_file = None

    if os.path.exists(specific_video_output_path) and os.path.isdir(specific_video_output_path):
        # Сортируем для более предсказуемого выбора, если несколько файлов подходят
        try:
            files_in_dir = sorted(list(os.scandir(specific_video_output_path)), key=lambda e: e.name)
        except OSError as e_scan:
            print(f"Ошибка при сканировании директории {specific_video_output_path}: {e_scan}")
            files_in_dir = [] # Продолжаем без файлов, если директория недоступна

        # Сначала ищем файлы в предпочтительном порядке расширений
        for ext_pref in preferred_order:
            for entry in files_in_dir:
                if entry.is_file() and entry.name.lower().endswith(ext_pref):
                    candidate_audio_file = entry.path
                    break 
            if candidate_audio_file:
                break
        
        # Если не найдены предпочтительные, ищем любые другие известные аудиоформаты
        if not candidate_audio_file:
            for entry in files_in_dir:
                if entry.is_file():
                    file_ext = os.path.splitext(entry.name)[1].lower()
                    if file_ext in known_audio_extensions and file_ext not in preferred_order:
                        candidate_audio_file = entry.path
                        break 
    
    if candidate_audio_file:
        audio_file_path = candidate_audio_file
        print(f"Найден существующий аудиофайл: {audio_file_path}")
        print("Попытка получить метаданные для существующего файла...")
        try:
            yt_dlp_cmd = ['yt-dlp', '--skip-download', '--dump-json', video_url, '--no-warnings']
            if actual_cookies_file:
                yt_dlp_cmd.extend(['--cookies', actual_cookies_file])
            
            process = subprocess.Popen(yt_dlp_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, encoding='utf-8')
            stdout, stderr = process.communicate(timeout=60) # Таймаут для предотвращения зависания

            if process.returncode != 0:
                error_message_ytdlp = stderr.strip() if stderr else "yt-dlp process exited with code " + str(process.returncode)
                print(f"Ошибка выполнения yt-dlp для получения метаданных: {error_message_ytdlp}")
                update_transcription_status(video_id, 'error_ytdlp_metadata', error_message_ytdlp[:255])
                return

            raw_metadata_json = None
            try:
                raw_metadata_json = json.loads(stdout)
            except json.JSONDecodeError:
                lines = stdout.strip().split('\\n')
                if lines:
                    try:
                        raw_metadata_json = json.loads(lines[-1])
                    except json.JSONDecodeError as e_json_line:
                        print(f"Ошибка при разборе последней строки JSON метаданных из yt-dlp: {e_json_line}")
                        print(f"Полный вывод yt-dlp stdout:\\n{stdout}")
                        update_transcription_status(video_id, 'error_parsing_metadata_line', str(e_json_line))
                        return
                else:
                    print("yt-dlp не вернул JSON метаданных.")
                    update_transcription_status(video_id, 'error_no_metadata_json', "yt-dlp stdout was empty or not JSON")
                    return
            
            if not raw_metadata_json or not isinstance(raw_metadata_json, dict):
                print(f"Не удалось получить корректные метаданные JSON от yt-dlp. Получено: {raw_metadata_json}")
                update_transcription_status(video_id, 'error_bad_metadata_format', f"Type: {type(raw_metadata_json)}")
                return

            # Формируем словарь metadata в ожидаемом формате
            metadata = {
                'title': raw_metadata_json.get('title'),
                'original_url': raw_metadata_json.get('webpage_url', video_url),
                'upload_date': raw_metadata_json.get('upload_date'), # YYYYMMDD
                'duration': raw_metadata_json.get('duration'), # seconds
                'uploader': raw_metadata_json.get('uploader'),
                'channel_name': raw_metadata_json.get('channel'),
                'channel_url': raw_metadata_json.get('channel_url'),
                'uploader_url': raw_metadata_json.get('uploader_url'),
                'video_id': raw_metadata_json.get('id', video_id),
                'description': raw_metadata_json.get('description'),
                'thumbnail_url': raw_metadata_json.get('thumbnail'),
                'view_count': raw_metadata_json.get('view_count'),
                'like_count': raw_metadata_json.get('like_count'),
                'full_yt_dlp_json': raw_metadata_json 
            }
            if not metadata['uploader'] and metadata['channel_name']:
                metadata['uploader'] = metadata['channel_name']
            if not metadata['uploader_url'] and metadata['channel_url']:
                metadata['uploader_url'] = metadata['channel_url']
                
            print(f"Метаданные успешно получены для существующего файла. Название: {metadata.get('title', 'N/A')}")

        except subprocess.TimeoutExpired:
            print("Таймаут при выполнении yt-dlp для получения метаданных.")
            update_transcription_status(video_id, 'error_ytdlp_timeout_metadata', "yt-dlp metadata fetch timed out")
            return
        except Exception as e_meta_generic:
            print(f"Неизвестная ошибка при получении метаданных для существующего файла: {e_meta_generic}")
            import traceback
            traceback.print_exc()
            update_transcription_status(video_id, 'error_metadata_generic_existing', str(e_meta_generic)[:255])
            return
    else:
        print(f"Аудиофайл не найден в {specific_video_output_path}. Будет выполнена загрузка.")
        try:
            print(f"\n--- Начало загрузки аудио для: {video_url} (ID: {video_id}) ---")
            
            audio_file_path_dl, metadata_dl = downloader.download_video(
                video_url, 
                specific_video_output_path, 
                cookies_file=actual_cookies_file
            )
            
            if audio_file_path_dl and metadata_dl:
                audio_file_path = audio_file_path_dl
                metadata = metadata_dl
                print(f"Аудио загружено: {audio_file_path}")
                if metadata: # Дополнительная проверка, хотя и в условии выше
                    print(f"Метаданные получены. Название: {metadata.get('title', 'N/A')}")
                print(f"Данные сохранены в: {video_data_folder}")
            elif not audio_file_path_dl:
                print("VideoDownloader не вернул путь к аудио файлу.")
                update_transcription_status(video_id, 'error_downloading_no_path', "VideoDownloader did not return audio file path")
                return
            elif not metadata_dl: # audio_file_path_dl существует, но metadata_dl отсутствует
                print("VideoDownloader не вернул метаданные (но аудиофайл мог быть скачан).")
                audio_file_path = audio_file_path_dl # Сохраняем путь, если он есть
                update_transcription_status(video_id, 'error_downloading_no_metadata', "VideoDownloader did not return metadata")
                # Решаем, стоит ли продолжать без метаданных. Для save_transcription_data они нужны.
                return
            else: # Неожиданный случай, если audio_file_path_dl или metadata_dl имеют "ложное" значение, но не None
                print("VideoDownloader вернул некорректные данные (путь или метаданные).")
                update_transcription_status(video_id, 'error_downloading_invalid_data', "VideoDownloader returned invalid data")
                return

        except Exception as e_download:
            print(f"Ошибка при загрузке через VideoDownloader: {e_download}")
            import traceback
            traceback.print_exc()
            if video_id: 
                update_transcription_status(video_id, 'error_downloading_main', str(e_download)[:255])
            return

    # --- Валидация после Этапа 1 ---
    if not audio_file_path or not os.path.exists(audio_file_path):
        print(f"Критическая ошибка: Аудиофайл отсутствует или путь не установлен ({audio_file_path}). Прерывание.")
        if video_id : 
             update_transcription_status(video_id, 'error_audio_file_missing_post_setup', f"Audio file {audio_file_path} not found or path not set after setup stage")
        return

    if not metadata:
        print("Критическая ошибка: Метаданные отсутствуют. Прерывание.")
        if video_id:
             update_transcription_status(video_id, 'error_metadata_missing_post_setup', "Metadata not obtained after setup stage")
        return
    
    # --- Этап 2: Транскрибация аудио ---
    transcript_text = None
    transcription_result_obj = None # Для хранения всего объекта результата
    try:
        print(f"\n--- Начало транскрибации аудио: {audio_file_path} ---")
        
        # Определяем промпт для транскрибации
        prompt_text = args.prompt  # Берем промпт из аргумента командной строки, если указан
        
        # Если указан файл с промптом, читаем его
        if args.prompt_file:
            prompt_file_path = args.prompt_file
            try:
                full_prompt_file_path = None
                
                # Приоритетные папки для поиска промптов
                prompt_search_dirs = [
                    os.path.join(PROJECT_ROOT, "src", "transcriber", "prompts"),  # Специальная папка для промптов
                    os.path.join(PROJECT_ROOT, "src", "transcriber"),             # Папка транскрибера
                    PROJECT_ROOT,                                                 # Корень проекта
                    os.path.dirname(os.path.abspath(__file__))                    # Директория, где находится main.py
                ]
                
                # Проверяем, является ли путь абсолютным
                if os.path.isabs(prompt_file_path):
                    if os.path.exists(prompt_file_path):
                        full_prompt_file_path = prompt_file_path
                        print(f"Используется абсолютный путь к файлу промпта: {full_prompt_file_path}")
                    else:
                        print(f"Предупреждение: Файл промпта по абсолютному пути '{prompt_file_path}' не найден.")
                else:
                    # Сначала пробуем найти файл относительно текущей директории
                    if os.path.exists(prompt_file_path):
                        full_prompt_file_path = os.path.abspath(prompt_file_path)
                        print(f"Файл промпта найден в текущей директории: {full_prompt_file_path}")
                    else:
                        # Ищем файл в приоритетных директориях
                        for search_dir in prompt_search_dirs:
                            potential_path = os.path.join(search_dir, prompt_file_path)
                            if os.path.exists(potential_path):
                                full_prompt_file_path = potential_path
                                print(f"Файл промпта найден в: {full_prompt_file_path}")
                                break
                        
                        if not full_prompt_file_path:
                            # Если не нашли, проверяем существование директорий для промптов и сообщаем пользователю
                            prompts_dir = os.path.join(PROJECT_ROOT, "src", "transcriber", "prompts")
                            if not os.path.exists(prompts_dir):
                                os.makedirs(prompts_dir, exist_ok=True)
                                print(f"Создана директория для промптов: {prompts_dir}")
                                
                            print(f"Предупреждение: Файл промпта '{prompt_file_path}' не найден.")
                            print(f"Рекомендуемые места для файлов промптов: {prompts_dir}")
                
                if full_prompt_file_path and os.path.exists(full_prompt_file_path):
                    with open(full_prompt_file_path, 'r', encoding='utf-8') as pf:
                        prompt_text = pf.read().strip()
                    print(f"Промпт загружен из файла: {full_prompt_file_path}")
                    
                    # Проверка на пустой промпт
                    if not prompt_text.strip():
                        print("Предупреждение: Загруженный файл промпта пуст.")
                else:
                    print("Файл промпта не найден, будет использован промпт из аргумента --prompt или значение по умолчанию.")
            except Exception as e_prompt:
                print(f"Ошибка при чтении файла промпта: {e_prompt}")
                # Оставляем prompt_text как есть (из аргумента --prompt или None)
        
        # Определяем опции для провайдера
        provider_options = {
            'language': 'ru', # Пример: 'ru' или 'en', или None для автодетекта (если провайдер поддерживает)
            'with_timestamps': args.timestamps, # Запрашивать ли таймкоды - берем из аргумента командной строки
            'prompt_text': prompt_text, # Промпт для улучшения качества - из аргумента или файла
            # 'model_name': 'gemini-1.5-pro' # Если хотим указать конкретную модель для Gemini
        }

        transcription_result_obj = transcription_service.transcribe(
            audio_file_path=audio_file_path,
            provider_name='gemini', # По умолчанию используем Gemini
            api_key=args.transcription_api_key, # Передаем ключ из аргументов
            provider_options=provider_options
        )

        if transcription_result_obj and transcription_result_obj.success:
            transcript_text = transcription_result_obj.text
            print("Транскрибация успешно завершена.")

            # --- Сохранение транскрипции в .txt файл ---
            try:
                now = datetime.now()
                timestamp_str = now.strftime("%d-%m-%y_%H-%M") # Формат для имени файла ДД-ММ-ГГ_ЧЧ-ММ
                
                # Базовое имя файла из video_id
                base_txt_filename = video_id 
                
                # Полное имя файла транскрипции
                txt_file_name = os.path.join(os.path.dirname(audio_file_path), f"{timestamp_str}_{base_txt_filename}.txt")

                # Подготовка содержимого для файла
                content_lines = []

                # 1. Дата и время создания файла
                formatted_datetime_content = now.strftime("%d-%m-%Y %H:%M:%S") # Более полный формат для содержимого
                content_lines.append(f"Дата и Время создания файла: {formatted_datetime_content}\n")
                
                # 2. Информация о токенах (технические данные) - разместим их в начале файла
                token_info = transcription_result_obj.token_info
                if token_info:
                    content_lines.append("--- Информация о токенах ---")
                    
                    if isinstance(token_info, dict):
                        # Отображение известных ключей с русскими метками как в примере
                        token_display_map = {
                            "input_token_count": "Входные токены (оценка)",
                            "output_token_count": "Выходные токены",
                            "total_token_count": "Общие токены (API)",
                        }
                        
                        # Порядок как в примере, если ключи существуют
                        key_order = ["input_token_count", "output_token_count", "total_token_count"]
                        
                        for key in key_order:
                            if key in token_info:
                                label = token_display_map.get(key, key)
                                content_lines.append(f"{label}: {token_info[key]}")
                        
                        # Добавление других ключей, если они есть (например, estimated_input_cost)
                        for key, value in token_info.items():
                            if key not in key_order:
                                # Попробуем использовать метку, если есть, или сам ключ
                                label = token_display_map.get(key, key.replace('_', ' ').capitalize())
                                content_lines.append(f"{label}: {value}")
                    
                    elif isinstance(token_info, str):
                        # Если это строка, разбиваем ее по переносам и добавляем каждую строку
                        for line in token_info.strip().split('\n'):
                            content_lines.append(line)
                            
                    else:
                        # Для других типов просто преобразуем в строку
                        content_lines.append(str(token_info))

                    content_lines.append("(Примечание: Оценка входных токенов может отличаться от фактического значения, рассчитанного API.)")
                    content_lines.append("---------------------------\n")

                # 3. Использованный промпт
                prompt_used = provider_options.get('prompt_text')
                content_lines.append("--- Использованный промпт ---")
                if prompt_used and prompt_used.strip():
                    content_lines.append(prompt_used)
                else:
                    content_lines.append("Промпт не указан.")
                content_lines.append("-----------------------------\n")

                # 4. Текст транскрипции (сам ответ)
                content_lines.append(transcript_text)

                with open(txt_file_name, 'w', encoding='utf-8') as f:
                    f.write("\n".join(content_lines))
                print(f"Транскрипция сохранена в файл: {txt_file_name}")
            except Exception as e_save:
                print(f"Ошибка при сохранении транскрипции в файл: {e_save}")
            # --- Конец сохранения в .txt файл ---

            if transcription_result_obj.token_info: # Вывод в консоль
                if isinstance(transcription_result_obj.token_info, dict):
                    token_info_str = ", ".join([f"{k}: {v}" for k, v in transcription_result_obj.token_info.items()])
                    print(f"Информация о токенах: {token_info_str}")
                else:
                    print(f"Информация о токенах:\n{transcription_result_obj.token_info}")
        else:
            error_msg = transcription_result_obj.error_message if transcription_result_obj else "Неизвестная ошибка транскрибации"
            raise Exception(f"Ошибка транскрибации: {error_msg}")

    except Exception as e:
        print(f"Ошибка при транскрибации аудио: {e}")
        if video_id:
            error_detail = str(e)
            if transcription_result_obj and transcription_result_obj.error_message:
                 error_detail = transcription_result_obj.error_message
            update_transcription_status(video_id, 'error_transcribing', error_detail)
        return

    # --- Этап 3: Сохранение данных в БД ---
    if video_id and transcript_text and metadata:
        try:
            print(f"\n--- Начало сохранения данных в БД для видео: {video_id} ---")
            # Определяем, какой текст передавать в какой столбец
            main_transcription_to_save = transcript_text # По умолчанию основной текст
            gemini_specific_transcription = None

            # Если транскрибация была от Gemini, сохраняем ее в специальный столбец
            # и также дублируем в основной столбец `transcription` для обратной совместимости
            # или если другие провайдеры не используются.
            # Если в будущем будут другие провайдеры, логику можно будет усложнить.
            current_provider_name = 'gemini' # В данном случае мы знаем, что это Gemini
            if current_provider_name == 'gemini':
                gemini_specific_transcription = transcript_text

            # Собираем метаданные для сохранения
            # Убедимся, что все ключи существуют или имеют значения по умолчанию
            video_title = metadata.get('title', 'N/A')
            original_video_url = metadata.get('original_url', video_url) # Используем исходный URL, если нет в метаданных
            upload_date_str = metadata.get('upload_date') # Формат YYYYMMDD или None
            duration_secs = metadata.get('duration') # В секундах или None
            uploader_name = metadata.get('uploader', 'N/A')
            uploader_url = metadata.get('uploader_url', 'N/A')
            prompt_text_used = provider_options.get('prompt_text')

            record_id = save_transcription_data(
                video_id=video_id,
                title=video_title,
                original_url=original_video_url,
                upload_date_str=upload_date_str, 
                duration_seconds=duration_secs,
                channel_name=uploader_name,
                channel_url=uploader_url,
                audio_file_path=audio_file_path,
                video_data_folder=video_data_folder,
                transcription_text=main_transcription_to_save, # Основной текст
                gemini_transcription_text=gemini_specific_transcription, # Текст от Gemini
                transcription_prompt_text=prompt_text_used,
                full_metadata=metadata, # Полные метаданные как JSON
                status='transcribed'
            )
            if record_id:
                print(f"Данные успешно сохранены в БД. ID записи: {record_id}")
            else:
                raise Exception("Функция save_transcription_data не вернула ID записи.")

        except Exception as e:
            print(f"Ошибка при сохранении данных в БД (таблица transcripts): {e}")
            if video_id:
                update_transcription_status(video_id, 'error_saving_to_db', str(e))
    else:
        print("Не все данные доступны для сохранения в БД (аудио, метаданные или транскрипция отсутствуют).")

if __name__ == "__main__":
    # Перед запуском убедитесь, что:
    # 1. Установлены все зависимости из requirements.txt
    # 2. Создан файл .env с YOUTUBE_API_KEY, GEMINI_API_KEY и данными для подключения к БД
    #    (см. .env.example)
    # 3. Локальный сервер PostgreSQL (Supabase) запущен и доступен.
    # 4. (Опционально) Файл cookies.txt находится в корне проекта или указан через аргумент,
    #    если требуется для доступа к видео.
    
    try:
        main()
    except Exception as e:
        print(f"Критическая ошибка при выполнении скрипта: {e}")
        import traceback
        traceback.print_exc()