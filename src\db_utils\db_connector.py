# src/db_utils/db_connector.py
import psycopg2
import os
from dotenv import load_dotenv

# Загрузка переменных окружения для данных подключения к БД
load_dotenv()

DB_NAME = os.getenv("DB_NAME", "postgres")
DB_USER = os.getenv("DB_USER", "postgres")
DB_PASSWORD = os.getenv("DB_PASSWORD") # Пароль обязателен
DB_HOST = os.getenv("DB_HOST", "localhost")
DB_PORT = os.getenv("DB_PORT", "5432")

def get_db_connection():
    """Устанавливает соединение с базой данных PostgreSQL."""
    if not DB_PASSWORD:
        print("Ошибка подключения: Пароль для базы данных (DB_PASSWORD) не установлен в переменных окружения.")
        # Вместо raise ValueError, чтобы не прерывать другие модули при импорте, 
        # лучше вернуть None и позволить вызывающему коду обработать это.
        return None 
        
    try:
        conn = psycopg2.connect(
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT
        )
        print(f"DB_Connector: Соединение с БД ({DB_HOST}:{DB_PORT}, база: {DB_NAME}) установлено.")
        return conn
    except psycopg2.OperationalError as e:
        print(f"DB_Connector: Ошибка подключения к базе данных: {e}")
        print(f"Используемые параметры: DB_HOST={DB_HOST}, DB_PORT={DB_PORT}, DB_NAME={DB_NAME}, DB_USER={DB_USER}")
        return None # Возвращаем None в случае ошибки

if __name__ == '__main__':
    print("Тестирование db_connector.py...")
    connection = get_db_connection()
    if connection:
        print("Соединение успешно установлено и закрыто.")
        connection.close()
    else:
        print("Не удалось установить соединение.") 