
"""
Базовые тесты для YouTube Apify скрапера
"""

import unittest
import sys
import os
import json
from unittest.mock import patch, MagicMock

# Добавляем корневую директорию проекта в путь для импорта
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.scrapers.youtube_apify import YouTubeScraper

class TestYouTubeScraper(unittest.TestCase):
    """Тесты для класса YouTubeScraper"""
    
    def setUp(self):
        """Настройка перед каждым тестом"""
        self.scraper = YouTubeScraper(api_token="test_token")
    
    @patch('src.scrapers.youtube_apify.ApifyClient')
    def test_search_by_query(self, mock_apify_client):
        """Тест метода search_by_query"""
        # Настраиваем моки
        mock_actor_call = MagicMock()
        mock_actor_call.get.return_value = 'test_id'
        
        mock_actor = MagicMock()
        mock_actor.call.return_value = mock_actor_call
        
        mock_client_instance = MagicMock()
        mock_client_instance.actor.return_value = mock_actor
        
        mock_run = MagicMock()
        mock_run.get.return_value = {'status': 'SUCCEEDED'}
        mock_client_instance.run.return_value = mock_run
        
        mock_items = MagicMock()
        mock_items.items = [{'title': 'Test Video'}]
        mock_client_instance.dataset().list_items.return_value = mock_items
        
        mock_apify_client.return_value = mock_client_instance
        
        # Выполняем тестируемый метод
        results = self.scraper.search_by_query("test query", max_results=1)
        
        # Проверяем, что метод вернул ожидаемый результат
        self.assertEqual(results, [{'title': 'Test Video'}])
        
        # Проверяем, что метод вызвал правильные методы клиента с правильными параметрами
        mock_client_instance.actor.assert_called_once_with('streamers/youtube-scraper')
        mock_actor.call.assert_called_once()
        self.assertIn('searchQueries', mock_actor.call.call_args[1]['run_input'])
        self.assertEqual(mock_actor.call.call_args[1]['run_input']['searchQueries'], ['test query'])
    
    def test_print_video_info(self):
        """Тест метода print_video_info"""
        # Тестовые данные
        video_data = {
            'title': 'Test Video',
            'url': 'https://www.youtube.com/watch?v=123456',
            'channelName': 'Test Channel',
            'viewCount': 1000,
            'date': '2023-01-01',
            'duration': '10:00'
        }
        
        # Этот тест просто проверяет, что метод не вызывает исключений
        try:
            self.scraper.print_video_info(video_data)
            self.assertTrue(True)  # Если мы дошли до этой точки, тест успешен
        except Exception as e:
            self.fail(f"print_video_info вызвал исключение {e}")
    
    @patch('builtins.open', new_callable=unittest.mock.mock_open)
    @patch('json.dump')
    def test_save_results(self, mock_json_dump, mock_open):
        """Тест метода save_results"""
        # Тестовые данные
        results = [{'title': 'Test Video'}]
        
        # Выполняем тестируемый метод
        success = self.scraper.save_results(results, 'test_output.json')
        
        # Проверяем, что метод вернул True (успех)
        self.assertTrue(success)
        
        # Проверяем, что файл был открыт с правильным именем и режимом
        mock_open.assert_called_once_with('test_output.json', 'w', encoding='utf-8')
        
        # Проверяем, что json.dump был вызван с правильными параметрами
        mock_json_dump.assert_called_once()
        args, kwargs = mock_json_dump.call_args
        self.assertEqual(args[0], results)  # Первый аргумент - результаты
        self.assertEqual(kwargs['indent'], 2)  # Параметр indent должен быть 2
        self.assertEqual(kwargs['ensure_ascii'], False)  # ensure_ascii должен быть False

if __name__ == '__main__':
    unittest.main()
