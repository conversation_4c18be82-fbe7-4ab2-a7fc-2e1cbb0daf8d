
from setuptools import setup, find_packages

setup(
    name="youtube-downloader",
    version="0.1.0",
    author="AI Team",
    description="A YouTube scraper using various APIs",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/youtube_downloader",
    packages=find_packages("src"),
    package_dir={"": "src"},
    install_requires=[
        "apify-client>=1.0.0",
        "python-dotenv>=0.19.0",
    ],
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires=">=3.7",
)
