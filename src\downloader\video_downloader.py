"""
Модуль для скачивания видео с YouTube.
"""
import os
import sys
from urllib.parse import urlparse, parse_qs
import json
import subprocess
import time
from datetime import datetime
import requests

class VideoDownloader:
    def __init__(self, api_client=None, use_ffmpeg=False):
        """
        Инициализирует загрузчик видео.

        Args:
            api_client: Экземпляр YouTubeApiClient для получения метаданных (опционально).
            use_ffmpeg: Флаг, определяющий использование FFmpeg для конвертации (по умолчанию False)
        """
        self.api_client = api_client
        self.use_ffmpeg = use_ffmpeg
        print("VideoDownloader initialized")

    def extract_video_id(self, url):
        """
        Извлекает ID видео из URL YouTube.

        Args:
            url (str): URL видео на YouTube.

        Returns:
            str: ID видео или None, если не удалось извлечь.
        """
        # Примеры URL:
        # http://www.youtube.com/watch?v=xxxxxxxxxxx
        # http://youtu.be/xxxxxxxxxxx
        # https://youtube.com/embed/xxxxxxxxxxx
        # https://m.youtube.com/watch?v=xxxxxxxxxxx
        parsed_url = urlparse(url)
        if parsed_url.hostname == 'youtu.be':
            return parsed_url.path[1:]
        if parsed_url.hostname in ('www.youtube.com', 'youtube.com', 'm.youtube.com'):
            if parsed_url.path == '/watch':
                p = parse_qs(parsed_url.query)
                return p.get('v', [None])[0]
            if parsed_url.path.startswith('/embed/'):
                return parsed_url.path.split('/embed/')[1].split('?')[0]
            if parsed_url.path.startswith('/v/'): # очень старый формат
                return parsed_url.path.split('/v/')[1].split('?')[0]
        return None

    def download_video(self, video_id_or_url, output_path, cookies_file=None, yt_dlp_args=None):
        """
        Скачивает видео с YouTube.

        Args:
            video_id_or_url (str): URL видео на YouTube или ID видео.
            output_path (str): Путь для сохранения файла.
            cookies_file (str, optional): Путь к файлу cookies.
            yt_dlp_args (list, optional): Дополнительные аргументы для yt-dlp.

        Returns:
            tuple: (путь к скачанному файлу, метаданные видео)
        """

        # Определяем video_id и video_url_internal
        if "youtube.com/" in video_id_or_url or "youtu.be/" in video_id_or_url:
            video_url_internal = video_id_or_url
            video_id = self.extract_video_id(video_url_internal)
            if not video_id:
                raise ValueError(f"Не удалось извлечь ID видео из URL: {video_url_internal}")
        else: # Предполагаем, что это уже video_id
            video_id = video_id_or_url
            # Простая проверка, что ID похож на стандартный YouTube ID (11 символов, без пробелов и т.д.)
            # Это не строгая валидация, а базовая проверка.
            if not video_id or len(video_id) < 10 or len(video_id) > 15 or ' ' in video_id or '/' in video_id:
                 raise ValueError(f"Передан некорректный идентификатор видео: {video_id}")
            video_url_internal = f"https://www.youtube.com/watch?v={video_id}"

        print(f"[VideoDownloader] Обработка видео {video_id} (URL для загрузки: {video_url_internal}) в {output_path}")

        # Создаем директорию, если не существует
        os.makedirs(output_path, exist_ok=True)

        # Сначала получаем метаданные через YouTube API (если доступен)
        print("[VideoDownloader] Получение информации о видео...")
        metadata = None

        if self.api_client:
            try:
                print(f"[VideoDownloader] Получение метаданных через YouTube API для {video_id}")
                metadata = self.api_client.get_metadata_for_download(video_id)
                if metadata:
                    # Сохраняем метаданные
                    metadata_path = os.path.join(output_path, f"{video_id}_metadata.json")
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, ensure_ascii=False, indent=2)
                    print(f"[VideoDownloader] Метаданные сохранены в {metadata_path}")
            except Exception as api_err:
                print(f"[VideoDownloader] Ошибка при получении метаданных через API: {api_err}")

        # Если не удалось получить через API, пробуем через yt-dlp
        if not metadata:
            try:
                info_command = [
                    "yt-dlp",
                    "--dump-json",
                    video_url_internal
                ]
                # Добавляем cookies, если файл указан
                if cookies_file and os.path.exists(cookies_file):
                    info_command.extend(["--cookies", cookies_file])

                # Добавляем дополнительные аргументы для yt-dlp, если есть
                if yt_dlp_args:
                    info_command.extend(yt_dlp_args)

                info_result = subprocess.run(info_command, capture_output=True, text=True, check=True)
                metadata = json.loads(info_result.stdout)

                # Сохраняем метаданные в JSON
                metadata_path = os.path.join(output_path, f"{video_id}_metadata.json")
                with open(metadata_path, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, ensure_ascii=False, indent=2)
                print(f"[VideoDownloader] Метаданные сохранены в {metadata_path}")
            except Exception as e:
                print(f"[VideoDownloader] Ошибка при получении метаданных через yt-dlp: {e}")

        # Если все методы получения метаданных не сработали, создаем минимальные метаданные
        if not metadata:
            metadata = {
                'id': video_id,
                'title': f'Заголовок для {video_id}',
                'webpage_url': video_url_internal,
                'upload_date': '20230101',  # Заглушка
                'duration': 0,  # Заглушка
                'channel': 'Неизвестный канал',
                'channel_url': '',
            }

        # Загружаем аудио с помощью yt-dlp
        print(f"[VideoDownloader] Загрузка аудио из {video_url_internal}...")

        # Путь для выходного аудиофайла
        output_template = os.path.join(output_path, "%(id)s.%(ext)s")

        # Базовая команда yt-dlp для загрузки только аудио
        download_command = [
            "yt-dlp",
            "-f", "bestaudio",
            "-o", output_template,
            "--write-info-json",
            video_url_internal
        ]

        # Явное указание на конвертацию в MP3 через yt-dlp на этапе скачивания удалено.
        # yt-dlp будет загружать аудио в наилучшем доступном исходном формате.
        # Флаг self.use_ffmpeg больше не управляет добавлением аргументов для MP3 конвертации здесь.
        if self.use_ffmpeg:
            print("[VideoDownloader] self.use_ffmpeg=True: Явная MP3-конвертация через yt-dlp на этапе загрузки теперь отключена. Аудио будет загружено в исходном формате.")
            # Следующие строки, добавлявшие аргументы для конвертации в MP3, удалены:
            # download_command.extend([
            #     "--extract-audio",
            #     "--audio-format", "mp3",
            #     "--audio-quality", "192k"
            # ])
        else:
            print("[VideoDownloader] self.use_ffmpeg=False: FFmpeg отключен для yt-dlp, аудио будет скачано в оригинальном формате (как и ранее для этого случая).")

        # Добавляем cookies, если файл указан
        if cookies_file and os.path.exists(cookies_file):
            download_command.extend(["--cookies", cookies_file])

        # Добавляем дополнительные аргументы для yt-dlp, если есть
        if yt_dlp_args:
            download_command.extend(yt_dlp_args)

        try:
            subprocess.run(download_command, check=True)
            print("[VideoDownloader] Аудио успешно загружено.")
        except subprocess.CalledProcessError as e:
            raise RuntimeError(f"Ошибка при загрузке аудио: {e}")

        # Если FFmpeg отключен, ищем любой аудиофайл
        if not self.use_ffmpeg:
            audio_extensions = [".m4a", ".aac", ".ogg", ".opus", ".wav", ".flac", ".mp3", ".webm"]
            for ext in audio_extensions:
                audio_file_path = next(
                    (os.path.join(output_path, f) for f in os.listdir(output_path) if f.endswith(ext)),
                    None
                )
                if audio_file_path:
                    print(f"[VideoDownloader] Найден аудиофайл: {audio_file_path}")
                    break
        else:
            # Находим MP3 файл в выходной директории
            audio_file_path = next(
                (os.path.join(output_path, f) for f in os.listdir(output_path) if f.endswith(".mp3")),
                None
            )

            if not audio_file_path:
                # Если MP3 не найден, ищем любой аудиофайл, включая .webm
                audio_extensions = [".m4a", ".aac", ".ogg", ".opus", ".wav", ".flac", ".webm"]
                for ext in audio_extensions:
                    audio_file_path = next(
                        (os.path.join(output_path, f) for f in os.listdir(output_path) if f.endswith(ext)),
                        None
                    )
                    if audio_file_path:
                        break

        if not audio_file_path:
            raise FileNotFoundError("Аудиофайл не найден после загрузки.")

        # Дополнительно: загрузим обложку из метаданных
        if metadata and 'thumbnail' in metadata:
            thumbnail_url = metadata['thumbnail']
            thumbnail_path = os.path.join(output_path, f"{video_id}_thumbnail.jpg")
            try:
                response = requests.get(thumbnail_url)
                response.raise_for_status()
                with open(thumbnail_path, "wb") as f:
                    f.write(response.content)
                print(f"[VideoDownloader] Обложка сохранена: {thumbnail_path}")
            except Exception as e:
                print(f"[VideoDownloader] Ошибка при загрузке обложки: {e}")

        return audio_file_path, metadata