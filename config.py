# config.py

# Путь к файлу cookies для yt-dlp. Может быть None, если не используется.
# Оставим возможность задавать его здесь или через main.py
COOKIES_FILE_PATH = "cookies.txt" # Ожидается в корневой папке проекта

# Настройки для транскрибации (если нужно переопределить стандартный промпт)
# CUSTOM_TRANSCRIPTION_PROMPT_PATH = "custom_prompt.txt"

# Другие общие настройки могут быть добавлены здесь
# Например, уровень логирования, пути к временным файлам и т.д.

# Пример:
# LOG_LEVEL = "INFO"

# На данный момент этот файл больше для структуры, 
# основные чувствительные данные (API ключи, пароли БД) хранятся в .env

if __name__ == '__main__':
    print("Это файл конфигурации config.py")
    print(f"Предполагаемый путь к файлу cookies: {COOKIES_FILE_PATH}")