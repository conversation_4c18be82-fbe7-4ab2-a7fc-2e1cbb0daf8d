---
description: 
globs: 
alwaysApply: false
---
# Правила и основные принципы проекта YouTube Data Processor

Этот документ содержит основные сведения о структуре, технологиях и потоках данных проекта, предназначенные для помощи AI-ассистенту в навигации и внесении изменений.

## 1. Общая цель проекта

Автоматизация сбора данных с YouTube, их обработка (скачивание аудио, транскрибация) и сохранение результатов в базу данных PostgreSQL.

## 2. Ключевая структура директорий и файлов

*   **Основной исходный код**: `src/`
    *   **Главный оркестратор**: `[src/main.py](mdc:src/main.py)` - координирует полный цикл обработки видео.
    *   **Сбор данных (скрапинг)**: `src/scrapers/`
        *   Apify: `[src/scrapers/apify/youtube_apify.py](mdc:src/scrapers/apify/youtube_apify.py)`
        *   YouTube Data API:
            *   Базовые функции: `[src/scrapers/youtube_api/youtube_api.py](mdc:src/scrapers/youtube_api/youtube_api.py)`
            *   Клиентский класс: `[src/scrapers/youtube_api/youtube_api_client.py](mdc:src/scrapers/youtube_api/youtube_api_client.py)`
    *   **Загрузка видео/аудио**: `src/downloader/`
        *   Основной класс: `[src/downloader/video_downloader.py](mdc:src/downloader/video_downloader.py)` (использует `yt-dlp`).
        *   CLI-обертка для прямой загрузки аудио: `[download_youtube_audio.py](mdc:download_youtube_audio.py)` (в корне проекта).
    *   **Транскрибация**: `src/transcriber/`
        *   Центральный сервис: `[src/transcriber/transcription_service.py](mdc:src/transcriber/transcription_service.py)`
        *   Базовый класс провайдера: `[src/transcriber/base_provider.py](mdc:src/transcriber/base_provider.py)`
        *   Провайдеры (например, Gemini): `src/transcriber/providers/`
            *   Gemini: `[src/transcriber/providers/gemini_provider.py](mdc:src/transcriber/providers/gemini_provider.py)`
        *   Работа с БД транскрипций: `[src/transcriber/transcription_db.py](mdc:src/transcriber/transcription_db.py)`
        *   Независимый CLI для Gemini: `[src/transcriber/Gemini/gemini_transcriber.py](mdc:src/transcriber/Gemini/gemini_transcriber.py)`
    *   **Утилиты для БД**: `src/db_utils/`
        *   Коннектор: `[src/db_utils/db_connector.py](mdc:src/db_utils/db_connector.py)`
        *   Тест соединения: `[src/db_utils/pg_test_connection.py](mdc:src/db_utils/pg_test_connection.py)`
*   **Примеры использования модулей**: `examples/`
    *   Apify поиск: `[examples/youtube_search.py](mdc:examples/youtube_search.py)`
    *   YouTube API CLI: `[examples/search_cli.py](mdc:examples/search_cli.py)`
    *   YouTube API интерактивный: `[examples/search_interactive.py](mdc:examples/search_interactive.py)`
*   **Конфигурация**:
    *   Конфиденциальные данные: `[.env](mdc:.env)` (также см. `[.env.example](mdc:.env.example)`)
    *   Неконфиденциальные настройки: `[config.py](mdc:config.py)` (в корне проекта)
*   **Зависимости**: `[requirements.txt](mdc:requirements.txt)`

## 3. Ключевые технологии

*   **Язык**: Python 3.x
*   **База данных**: PostgreSQL
*   **Загрузка аудио**: `yt-dlp`
*   **Транскрибация**: Gemini API (через `TranscriptionService` или прямой CLI)
*   **Управление зависимостями**: `pip` и `[requirements.txt](mdc:requirements.txt)`
*   **Переменные окружения**: `python-dotenv` для `[.env](mdc:.env)`

## 4. Основной поток данных (через `src/main.py`)

1.  **Вход**: ID видео или URL.
2.  **Загрузка аудио**: `[src/downloader/video_downloader.py](mdc:src/downloader/video_downloader.py)` скачивает аудиодорожку.
3.  **Транскрибация**: `[src/transcriber/transcription_service.py](mdc:src/transcriber/transcription_service.py)` обрабатывает аудио и получает текст.
4.  **Сохранение в БД**: Результаты (метаданные, путь к аудио, текст транскрипции) сохраняются в таблицу `transcripts` PostgreSQL с помощью `[src/transcriber/transcription_db.py](mdc:src/transcriber/transcription_db.py)`.

## 5. Важные принципы

*   **Модульность**: Код организован в логические модули по их назначению. При добавлении функционала следовать этой структуре.
*   **Конфигурация**:
    *   API ключи, учетные данные БД и другие секреты хранятся **только** в `[.env](mdc:.env)`.
    *   Доступ к ним осуществляется через `os.getenv()`.
*   **База данных**:
    *   Общее подключение к БД через `get_db_connection()` из `[src/db_utils/db_connector.py](mdc:src/db_utils/db_connector.py)`.
    *   Работа с конкретными таблицами инкапсулирована в соответствующих модулях (например, `[src/transcriber/transcription_db.py](mdc:src/transcriber/transcription_db.py)` для таблицы `transcripts`).
*   **Обработка ошибок**: Важно обеспечивать адекватную обработку ошибок и логирование, особенно при взаимодействии с внешними API и базой данных.
*   **Документация**: Основной источник информации о проекте - `[README.md](mdc:README.md)`.

Эти правила помогут AI-ассистенту лучше понимать структуру проекта и эффективнее помогать в разработке.

