import os
import argparse
import google.generativeai as genai
from rich.console import Console
from rich.panel import Panel
from dotenv import load_dotenv
import datetime # Добавляем импорт datetime

def transcribe_audio(api_key, audio_file_path, with_timestamps=False, 
                     use_prompt=False, prompt_file=None, language=None):
    """
    Транскрибация аудиофайла с использованием Google Gemini 2.0 Flash.
    
    Параметры:
    - api_key: Ключ API для Gemini
    - audio_file_path: Путь к аудиофайлу
    - with_timestamps: Запрашивать таймкоды в транскрипции
    - use_prompt: Использовать расширенный промпт
    - prompt_file: Путь к файлу с промптом (если None, ищет файлы prompt.txt или transcript_prompt.txt)
    - language: Язык для транскрибации (например, 'ru' для русского, 'en' для английского)
    
    Возвращает:
    - tuple: (transcript, token_info) или (None, None) в случае ошибки
    """
    console = Console()
    
    try:
        # Конфигурация API ключа
        genai.configure(api_key=api_key)
        
        console.print("[bold blue]Конфигурация API ключа успешна[/bold blue]")
        console.print(f"[bold green]Обрабатываем файл:[/bold green] {audio_file_path}")
        
        # Проверка существования файла
        if not os.path.exists(audio_file_path):
            console.print(f"[bold red]Ошибка:[/bold red] Файл не найден: {audio_file_path}")
            return None, None
        
        # Определение типа файла на основе расширения
        file_extension = os.path.splitext(audio_file_path)[1].lower()
        mime_types = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.aiff': 'audio/aiff',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.flac': 'audio/flac'
        }
        
        mime_type = mime_types.get(file_extension)
        if not mime_type:
            console.print(f"[bold yellow]Предупреждение:[/bold yellow] Неизвестный формат файла. Попытка определить автоматически.")
            mime_type = 'audio/mpeg'  # Предполагаем MP3 по умолчанию
        
        # Определение размера файла
        file_size = os.path.getsize(audio_file_path) / (1024 * 1024)  # В МБ
        console.print(f"Размер файла: {file_size:.2f} МБ")
        
        # Формирование базового промпта
        if use_prompt:
            # Определение файла промпта
            if prompt_file is None:
                # Поиск файлов промпта
                possible_files = ["prompt.txt", "transcript_prompt.txt"]
                for file in possible_files:
                    if os.path.exists(file):
                        prompt_file = file
                        break
            
            if prompt_file and os.path.exists(prompt_file):
                console.print(f"[bold blue]Используем промпт из файла:[/bold blue] {prompt_file}")
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    prompt = f.read().strip()
            else:
                console.print("[bold yellow]Предупреждение:[/bold yellow] Файл промпта не найден. Используем базовый промпт.")
                prompt = "Создай полную транскрипцию этого аудио."
        else:
            prompt = "Создай полную транскрипцию этого аудио."
        
        # Добавление запроса на таймкоды, если нужно
        if with_timestamps and "таймкод" not in prompt.lower() and "timestamp" not in prompt.lower():
            prompt += " Добавь таймкоды формата [MM:SS] каждые 30 секунд."
        
        # Добавление указания языка
        if language:
            # Словарь полных названий языков
            language_names = {
                'ru': 'русский',
                'en': 'английский',
                'fr': 'французский',
                'de': 'немецкий',
                'es': 'испанский',
                'it': 'итальянский',
                'pt': 'португальский',
                'zh': 'китайский',
                'ja': 'японский'
            }
            
            language_name = language_names.get(language.lower(), language)
            console.print(f"[bold blue]Язык результата:[/bold blue] {language_name}")
            # Combine file prompt and language instruction with a newline
            language_instruction = f"Результат транскрибации должен быть на {language_name} языке."
            prompt = prompt + "\n" + language_instruction
        
        console.print(f"[bold blue]Используемый промпт:[/bold blue] {prompt}")
        
        # Инициализация модели
        model = genai.GenerativeModel('gemini-2.0-flash') # Перенесено выше для использования в count_tokens

        # Выбор метода загрузки в зависимости от размера файла
        if file_size > 20:
            console.print("Размер файла больше 20 МБ, используем метод upload...")
            # Загрузка файла
            audio_file = genai.upload_file(path=audio_file_path)
            console.print(f"Файл '{audio_file.display_name}' загружен.")
            
            # Подсчет входных токенов
            try:
                count_response = model.count_tokens([prompt, audio_file])
                input_token_count = count_response.total_tokens
                console.print(f"[bold yellow]Примерное количество входных токенов:[/bold yellow] {input_token_count}")
            except Exception as count_e:
                console.print(f"[bold red]Ошибка при подсчете входных токенов:[/bold red] {str(count_e)}")
                input_token_count = "Не удалось подсчитать"

            console.print("[bold blue]Начинаем транскрибацию...[/bold blue]")
            
            # Запрос на транскрибацию
            response = model.generate_content([prompt, audio_file])
        else:
            console.print("Файл меньше 20 МБ, используем inline метод...")
            # Чтение аудиофайла в бинарном режиме
            with open(audio_file_path, 'rb') as f:
                audio_bytes = f.read()
            
            # Подсчет входных токенов
            try:
                # Используем словарь напрямую для подсчета токенов
                audio_part_for_count = {
                    "mime_type": mime_type,
                    "data": audio_bytes
                }
                count_response = model.count_tokens([prompt, audio_part_for_count])
                input_token_count = count_response.total_tokens
                console.print(f"[bold yellow]Примерное количество входных токенов:[/bold yellow] {input_token_count}")
            except Exception as count_e:
                console.print(f"[bold red]Ошибка при подсчете входных токенов:[/bold red] {str(count_e)}")
                input_token_count = "Не удалось подсчитать"

            console.print("[bold blue]Начинаем транскрибацию...[/bold blue]")
            
            # Запрос на транскрибацию с inline данными
            audio_part = {
                "mime_type": mime_type,
                "data": audio_bytes
            }
            response = model.generate_content([prompt, audio_part])
        
        # Получение результата
        transcript = response.text
        
        console.print("[bold green]Транскрибация завершена![/bold green]")

        # Получение информации об использовании токенов из ответа
        try:
            output_token_count = response.usage_metadata.candidates_token_count
            total_token_count = response.usage_metadata.total_token_count
            console.print(f"[bold yellow]Количество выходных токенов:[/bold yellow] {output_token_count}")
            # Общее количество может отличаться от суммы входа+выхода из-за внутренних процессов
            console.print(f"[bold yellow]Общее количество токенов (по данным API):[/bold yellow] {total_token_count}") 
        except AttributeError:
            console.print("[bold yellow]Информация об использовании токенов недоступна в ответе API.[/bold yellow]")
            output_token_count = "Недоступно"
            total_token_count = "Недоступно"
        
        # Вывод результата
        console.print(Panel(transcript[:1000] + "..." if len(transcript) > 1000 else transcript, 
                           title="Фрагмент транскрипции", border_style="green"))
        
        # Формирование строки с информацией о токенах
        token_info = f"--- Информация о токенах ---\n"
        token_info += f"Входные токены (оценка): {input_token_count}\n"
        token_info += f"Выходные токены: {output_token_count}\n"
        token_info += f"Общие токены (API): {total_token_count}\n"
        token_info += f"(Примечание: Оценка входных токенов может отличаться от фактического значения, рассчитанного API.)\n"
        token_info += f"---------------------------\n\n"

        # Возвращаем транскрипт и информацию о токенах
        return transcript, token_info
        
    except Exception as e:
        console.print(f"[bold red]Ошибка при транскрибации:[/bold red] {str(e)}")
        return None, None

def main():
    load_dotenv() # Загрузка переменных окружения из .env файла
    console = Console()

    # Настройка парсера аргументов
    parser = argparse.ArgumentParser(description='Транскрибация аудиофайлов с помощью Gemini 2.0 Flash.')
    parser.add_argument('audio_file', help='Путь к аудиофайлу для транскрибации.')
    parser.add_argument('--api-key', help='Ключ API Gemini.') # Добавлен аргумент для API ключа
    parser.add_argument('--output-dir', choices=['root', 'Transcript'], default='root', 
                        help='Директория для сохранения результата: "root" (корневая папка) или "Transcript" (подпапка). По умолчанию: root.')
    parser.add_argument('--output-name', default='транскрипт', 
                        help='Базовое имя файла для сохранения результата (без расширения и даты). По умолчанию: транскрипт.')
    parser.add_argument('--timestamps', action='store_true', help='Добавить таймкоды в транскрипцию.')
    parser.add_argument('--prompt', action='store_true', help='Использовать расширенный промпт из файла (prompt.txt или transcript_prompt.txt).')
    parser.add_argument('--prompt-file', help='Указать конкретный файл с промптом.')
    parser.add_argument('--language', help='Язык для транскрибации (например, ru, en).')
    
    args = parser.parse_args()

    # Получение API ключа: сначала из аргумента, потом из окружения
    api_key = args.api_key
    if not api_key:
        api_key = os.getenv("GEMINI_API_KEY")
    
    if not api_key:
        console.print("[bold red]Ошибка:[/bold red] Ключ API Gemini не найден. Укажите его с помощью --api-key или установите переменную окружения GEMINI_API_KEY.")
        return

    # Получение текущей даты и времени и форматирование
    now = datetime.datetime.now()
    timestamp_str = now.strftime("%y-%m-%d_%H-%M") # Формат ГГ-ММ-ДД_ЧЧ-ММ

    # Формирование имени файла
    base_filename = args.output_name
    output_filename = f"{timestamp_str}_{base_filename}.txt"

    # Определение пути сохранения
    if args.output_dir == 'Transcript':
        output_directory = "Transcript"
        if not os.path.exists(output_directory):
            os.makedirs(output_directory)
            console.print(f"[bold blue]Создана директория:[/bold blue] {output_directory}")
        output_file_path = os.path.join(output_directory, output_filename)
    else: # args.output_dir == 'root'
        output_file_path = output_filename # Сохраняем в текущей директории

    # Вызов функции транскрибации
    transcript, token_info = transcribe_audio(
        api_key=api_key,
        audio_file_path=args.audio_file,
        with_timestamps=args.timestamps,
        use_prompt=args.prompt,
        prompt_file=args.prompt_file,
        language=args.language
    )

    # Сохранение результата, если транскрибация прошла успешно
    if transcript is not None and token_info is not None:
        try:
            with open(output_file_path, 'w', encoding='utf-8') as f:
                f.write(token_info)
                f.write(transcript)
            console.print(f"[bold green]Результат сохранен в файл:[/bold green] {output_file_path}")
        except IOError as e:
            console.print(f"[bold red]Ошибка при сохранении файла:[/bold red] {str(e)}")

if __name__ == "__main__":
    main()