-- SQL-запрос для создания таблицы youtube_videos_api с колонками, 
-- упорядоченными по значимости для контент-мар<PERSON><PERSON><PERSON>и<PERSON><PERSON><PERSON>

DROP TABLE IF EXISTS youtube_videos_api;

CREATE TABLE youtube_videos_api (
    -- Идентификационные данные
    id SERIAL PRIMARY KEY,
    video_id VARCHAR(255) UNIQUE NOT NULL,
    url VARCHAR(255),
    
    -- Основные метаданные контента
    title VARCHAR(255),
    description TEXT,
    published_at TIMESTAMP,
    duration VARCHAR(20),
    duration_seconds INTEGER,
    duration_formatted VARCHAR(20),
    
    -- Числовые метрики эффективности
    view_count BIGINT DEFAULT 0,
    like_count BIGINT DEFAULT 0,
    comment_count BIGINT DEFAULT 0,
    
    -- <PERSON><PERSON><PERSON><PERSON><PERSON>е о канале
    channel_id VARCHAR(255),
    channel_title VARCHAR(255),
    channel_url VARCHAR(255),
    
    -- Дополнительные метаданные
    thumbnail_url VARCHAR(255),
    category_id VARCHAR(50),
    tags JSONB DEFAULT '{}',
    is_caption_available VARCHAR(10),
    topic_categories JSONB DEFAULT '{}',
    is_age_restricted VARCHAR(10),
    is_monetized VARCHAR(10),
    
    -- Служебные данные
    data JSONB,
    retrieved_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Создаем индексы для ускорения поиска
CREATE INDEX idx_youtube_videos_api_video_id ON youtube_videos_api(video_id);
CREATE INDEX idx_youtube_videos_api_channel_id ON youtube_videos_api(channel_id);
CREATE INDEX idx_youtube_videos_api_published_at ON youtube_videos_api(published_at);
CREATE INDEX idx_youtube_videos_api_view_count ON youtube_videos_api(view_count);

-- Комментарий к таблице
COMMENT ON TABLE youtube_videos_api IS 'Таблица для хранения данных о видео с YouTube, упорядоченная по значимости для контент-маркетинга';

-- Комментарии к колонкам
COMMENT ON COLUMN youtube_videos_api.id IS 'Уникальный идентификатор записи в базе данных';
COMMENT ON COLUMN youtube_videos_api.video_id IS 'ID видео на YouTube';
COMMENT ON COLUMN youtube_videos_api.url IS 'URL видео на YouTube';
COMMENT ON COLUMN youtube_videos_api.title IS 'Название видео';
COMMENT ON COLUMN youtube_videos_api.description IS 'Описание видео';
COMMENT ON COLUMN youtube_videos_api.published_at IS 'Дата и время публикации видео';
COMMENT ON COLUMN youtube_videos_api.duration IS 'Длительность видео в формате ISO 8601 (PT1H2M3S)';
COMMENT ON COLUMN youtube_videos_api.duration_seconds IS 'Длительность видео в секундах';
COMMENT ON COLUMN youtube_videos_api.duration_formatted IS 'Длительность видео в читаемом формате (например, 1:02:03)';
COMMENT ON COLUMN youtube_videos_api.view_count IS 'Количество просмотров';
COMMENT ON COLUMN youtube_videos_api.like_count IS 'Количество лайков';
COMMENT ON COLUMN youtube_videos_api.comment_count IS 'Количество комментариев';
COMMENT ON COLUMN youtube_videos_api.channel_id IS 'ID канала YouTube';
COMMENT ON COLUMN youtube_videos_api.channel_title IS 'Название канала';
COMMENT ON COLUMN youtube_videos_api.channel_url IS 'URL канала';
COMMENT ON COLUMN youtube_videos_api.thumbnail_url IS 'URL миниатюры видео';
COMMENT ON COLUMN youtube_videos_api.category_id IS 'ID категории видео';
COMMENT ON COLUMN youtube_videos_api.tags IS 'Теги видео в формате JSONB';
COMMENT ON COLUMN youtube_videos_api.is_caption_available IS 'Доступность субтитров';
COMMENT ON COLUMN youtube_videos_api.topic_categories IS 'Категории тем в формате JSONB';
COMMENT ON COLUMN youtube_videos_api.is_age_restricted IS 'Наличие возрастных ограничений';
COMMENT ON COLUMN youtube_videos_api.is_monetized IS 'Монетизация видео';
COMMENT ON COLUMN youtube_videos_api.data IS 'Полные JSON-данные о видео';
COMMENT ON COLUMN youtube_videos_api.retrieved_at IS 'Время получения данных';
COMMENT ON COLUMN youtube_videos_api.created_at IS 'Время создания записи';
COMMENT ON COLUMN youtube_videos_api.updated_at IS 'Время обновления записи';
