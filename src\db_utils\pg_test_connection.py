import os
import psycopg2
from dotenv import load_dotenv
from .db_connector import get_db_connection, DB_HOST, DB_PORT, DB_NAME

def test_postgres_connection(table_name_to_test="youtube_videos_pg_test"):
    load_dotenv() # Убедимся, что переменные окружения загружены

    # conn_params и их проверка теперь инкапсулированы в get_db_connection
    # Однако, мы можем вывести информацию о параметрах, если get_db_connection вернет None
    print(f"Тест БД: Попытка подключения к PostgreSQL ({DB_HOST}:{DB_PORT}, база: {DB_NAME})...")

    conn = None # Инициализируем conn здесь, чтобы finally блок работал корректно
    cur = None
    try:
        conn = get_db_connection() # Используем новую функцию
        if not conn:
            print("Тест БД: Не удалось установить соединение через get_db_connection.")
            # Дополнительная информация о переменных окружения будет выведена в get_db_connection
            return False
            
        cur = conn.cursor()
        print("Тест БД: Соединение успешно установлено.")

        # 1. Проверка/Создание тестовой таблицы
        test_table_create_sql = f"""
        CREATE TABLE IF NOT EXISTS "{table_name_to_test}" (
            id SERIAL PRIMARY KEY,
            test_data TEXT,
            input_type TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        cur.execute(test_table_create_sql)
        conn.commit()
        print(f"Тест БД: Таблица '{table_name_to_test}' проверена/создана.")

        # 2. Запись тестовых данных
        test_input = "db_connection_test"
        test_value = "Hello, PostgreSQL!"
        cur.execute(f"INSERT INTO \"{table_name_to_test}\" (test_data, input_type) VALUES (%s, %s) RETURNING id;", (test_value, test_input))
        test_id = cur.fetchone()[0]
        conn.commit()
        print(f"Тест БД: Тестовая запись добавлена (ID: {test_id}).")

        # 3. Чтение тестовых данных
        cur.execute(f"SELECT test_data, input_type FROM \"{table_name_to_test}\" WHERE id = %s;", (test_id,))
        row = cur.fetchone()
        if row and row[0] == test_value and row[1] == test_input:
            print(f"Тест БД: Тестовая запись успешно прочитана: {row}")
        else:
            print(f"Тест БД: Ошибка чтения тестовой записи. Прочитано: {row}")
            conn.rollback()
            cur.close()
            conn.close()
            return False
        
        # 4. Удаление тестовых данных
        cur.execute(f"DELETE FROM \"{table_name_to_test}\" WHERE id = %s;", (test_id,))
        conn.commit()
        if cur.rowcount == 1:
            print(f"Тест БД: Тестовая запись (ID: {test_id}) успешно удалена.")
        else:
            print(f"Тест БД: Ошибка удаления тестовой записи (ID: {test_id}).")
            # Продолжаем, чтобы закрыть соединение

        print("Тест БД: Все операции успешно выполнены.")
        return True

    except psycopg2.Error as e:
        print(f"Тест БД: Ошибка PostgreSQL: {e}")
        return False
    except Exception as e:
        print(f"Тест БД: Непредвиденная ошибка: {e}")
        return False
    finally:
        if 'conn' in locals() and conn:
            if 'cur' in locals() and cur:
                cur.close()
            conn.close()
            print("Тест БД: Соединение закрыто.")

if __name__ == '__main__':
    print("Запуск теста соединения с базой данных PostgreSQL...")
    if test_postgres_connection():
        print("Тест соединения с БД пройден успешно!")
    else:
        print("Тест соединения с БД НЕ ПРОЙДЕН.") 