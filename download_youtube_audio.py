#!/usr/bin/env python
# download_youtube_audio.py - Скрипт для скачивания аудио с YouTube

import os
import argparse
import sys

# Добавляем корневую директорию проекта в путь импорта
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.downloader.video_downloader import VideoDownloader
from src.scrapers.youtube_api.youtube_api_client import YouTubeApiClient

def main():
    parser = argparse.ArgumentParser(description="Скачать аудио с YouTube")
    parser.add_argument("video", help="ID видео или URL YouTube")
    parser.add_argument("-o", "--output", help="Выходная директория (по умолчанию: downloads/<video_id>)", default=None)
    parser.add_argument("--use-ffmpeg", action="store_true", help="Использовать ffmpeg для конвертации в MP3 (по умолчанию отключено)")
    parser.add_argument("--cookies", help="Путь к файлу cookies.txt")
    parser.add_argument("--use-api", action="store_true", help="Использовать YouTube API")
    parser.add_argument("--api-key", help="API ключ YouTube")
    parser.add_argument("--ffmpeg-location", help="Путь к директории с ffmpeg (если не указан в PATH)")
    
    args = parser.parse_args()
    
    # Определяем ID видео (если передан URL)
    downloader = VideoDownloader()
    video_id = args.video
    if "youtube.com/" in args.video or "youtu.be/" in args.video:
        extracted_id = downloader.extract_video_id(args.video)
        if extracted_id:
            video_id = extracted_id
    
    # Задаем выходную директорию
    output_dir = args.output if args.output else f"downloads/{video_id}"
    
    # Инициализируем API клиент при необходимости
    api_client = None
    if args.use_api:
        api_key = args.api_key or os.getenv("YOUTUBE_API_KEY")
        if api_key:
            api_client = YouTubeApiClient(api_key=api_key)
        else:
            print("Предупреждение: --use-api указан, но API ключ не найден. Используйте --api-key или YOUTUBE_API_KEY")
    
    # Пересоздаем загрузчик с правильными параметрами
    downloader = VideoDownloader(api_client=api_client, use_ffmpeg=args.use_ffmpeg)
    
    try:
        print(f"Скачивание видео {video_id} в директорию {output_dir}")
        
        # Используем yt-dlp с указанием пути к ffmpeg, если он предоставлен
        yt_dlp_args = []
        if args.ffmpeg_location and args.use_ffmpeg:
            yt_dlp_args.extend(["--ffmpeg-location", args.ffmpeg_location])
            print(f"Используется ffmpeg из: {args.ffmpeg_location}")
        
        audio_path, metadata = downloader.download_video(
            video_id_or_url=args.video,
            output_path=output_dir,
            cookies_file=args.cookies,
            yt_dlp_args=yt_dlp_args
        )
        print(f"Успешно: аудио сохранено в {audio_path}")
        meta_file = os.path.join(output_dir, f"{video_id}_metadata.json")
        print(f"Метаданные сохранены в {meta_file}")
        
    except Exception as e:
        print(f"Ошибка: {e}")
        return 1
        
    return 0
    
if __name__ == "__main__":
    sys.exit(main()) 