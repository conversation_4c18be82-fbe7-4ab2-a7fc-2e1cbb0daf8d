"""
YouTube Scraper с использованием API Apify

Этот скрипт позволяет получать данные с YouTube через Apify API:
1. Поиск по ключевым словам
2. Получение видео с канала
3. Получение видео из плейлиста

Автор: Claude <PERSON>
Дата: 14.05.2025
"""

from dotenv import load_dotenv
load_dotenv() # Загружаем переменные из .env файла

import os
import json
import time
from apify_client import ApifyClient
import pandas as pd
import argparse
import sqlite3
import psycopg2
import psycopg2.extras
import re
import sys
# Импортируем новую функцию для соединения
from src.db_utils.db_connector import get_db_connection

class YouTubeScraper:
    def __init__(self, api_token=None):
        # Если токен не передан, пытаемся взять из .env файла или переменных окружения
        if api_token is None:
            api_token = os.getenv('APIFY_API_TOKEN', '**********************************************')
        
        self.client = ApifyClient(token=api_token)
        self.actor_id = 'streamers/youtube-scraper'
    
    def search_by_query(self, query, max_results=10):
        """Поиск видео по ключевому слову"""
        print(f"Поиск видео по запросу: '{query}'...")
        
        run_input = {
            'searchQueries': [query],
            'maxResults': max_results,
            'proxyConfiguration': {'useApifyProxy': True}
        }
        
        return self._run_scraper(run_input)
    
    def get_channel_videos(self, channel_url, max_results=20):
        """Получение видео с канала YouTube"""
        print(f"Получение видео с канала: {channel_url}...")
        
        run_input = {
            'startUrls': [{'url': channel_url}],
            'maxResults': max_results,
            'proxyConfiguration': {'useApifyProxy': True}
        }
        
        return self._run_scraper(run_input)
    
    def get_playlist_videos(self, playlist_url, max_results=20):
        """Получение видео из плейлиста YouTube"""
        print(f"Получение видео из плейлиста: {playlist_url}...")
        
        run_input = {
            'startUrls': [{'url': playlist_url}],
            'maxResults': max_results,
            'proxyConfiguration': {'useApifyProxy': True}
        }
        
        return self._run_scraper(run_input)
    
    def _run_scraper(self, run_input, max_wait_time=180):
        """Запуск скрапера и ожидание результатов"""
        try:
            # Запуск актора
            actor_call = self.client.actor(self.actor_id).call(run_input=run_input)
            run_id = actor_call.get('id')
            
            if not run_id:
                print("Ошибка: Не удалось получить ID запущенной задачи")
                return []
            
            print(f"Задача запущена (ID: {run_id}), ожидание результатов...")
            
            # Ожидание завершения задачи
            start_time = time.time()
            while True:
                if time.time() - start_time > max_wait_time:
                    print("Превышено время ожидания")
                    break
                
                status = self.client.run(run_id).get()
                status_name = status.get('status')
                print(f"Статус: {status_name}")
                
                if status_name in ['SUCCEEDED', 'FAILED', 'ABORTED', 'TIMED-OUT']:
                    print(f"Задача завершена со статусом: {status_name}")
                    
                    if status_name == 'SUCCEEDED':
                        # Получение результатов
                        dataset_id = actor_call.get('defaultDatasetId')
                        if dataset_id:
                            items = self.client.dataset(dataset_id).list_items().items
                            print(f"Получено результатов: {len(items)}")
                            return items
                    break
                
                time.sleep(5)
            
            return []
        
        except Exception as e:
            print(f"Произошла ошибка: {str(e)}")
            import traceback
            traceback.print_exc()
            return []
    
    def save_results(self, results, output_file_name='youtube_results.json'):
        """Сохранение результатов в JSON файл в папку output/"""
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        output_file_path = os.path.join(output_dir, output_file_name)
        try:
            with open(output_file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"Результаты сохранены в файл: {output_file_path}")
            return True
        except Exception as e:
            print(f"Ошибка при сохранении результатов: {str(e)}")
            return False
    
    def print_video_info(self, video_data):
        """Вывод информации о видео"""
        if not video_data:
            print("Нет данных для отображения")
            return
        
        print("\n" + "="*50)
        print(f"Название: {video_data.get('title', 'Нет названия')}")
        print(f"URL: {video_data.get('url', 'Нет URL')}")
        print(f"Канал: {video_data.get('channelName', 'Нет имени канала')}")
        print(f"Просмотры: {video_data.get('viewCount', 'Нет данных о просмотрах')}")
        print(f"Дата: {video_data.get('date', 'Нет даты')}")
        print(f"Длительность: {video_data.get('duration', 'Нет данных о длительности')}")
        print("="*50 + "\n")

    def save_results_table(self, results, output_file_name='youtube_results.csv'):
        """Сохраняет результаты в таблицу CSV в папку output/, каждая строка — отдельное видео, все параметры JSON учитываются."""
        if not results:
            print("Нет данных для сохранения в таблицу.")
            return False
        
        output_dir = "output"
        os.makedirs(output_dir, exist_ok=True)
        output_file_path = os.path.join(output_dir, output_file_name)
        
        try:
            # Преобразуем список словарей в DataFrame
            df = pd.json_normalize(results)

            # Добавим дополнительные столбцы для каталогизации, если их нет
            extra_fields = [
                ('channelId', 'ID канала'),
                ('url', 'URL видео'),
                ('channelUrl', 'URL канала'),
                ('channelName', 'Ник канала'),
                ('playlistId', 'ID плейлиста'),
                ('playlistTitle', 'Название плейлиста'),
            ]
            for field, col_name in extra_fields:
                if field not in df.columns:
                    df[col_name] = None
                else:
                    df.rename(columns={field: col_name}, inplace=True)

            # Сохраняем в CSV
            df.to_csv(output_file_path, index=False, encoding='utf-8-sig')
            print(f"Результаты сохранены в таблицу: {output_file_path}")
            return True
        except Exception as e:
            print(f"Ошибка при сохранении таблицы: {str(e)}")
            return False

    def save_results_db(self, results, search_query_term: str, table_name='youtube_videos_pg', input_type=None):
        """Сохраняет результаты в базу данных PostgreSQL с проверкой на дубли по ID видео.
        
        Args:
            results: Список результатов скрапинга
            search_query_term: Строка запроса или URL
            table_name: Имя таблицы в базе данных
            input_type: Тип скрапинга (search, channel, playlist) с запросом
        """
        if not results:
            print("Нет данных для сохранения в базу данных.")
            return False

        try:
            df = pd.json_normalize(results)
            df['search_query'] = search_query_term
            
            # Добавляем или обновляем столбец input с типом скрапинга
            if input_type:
                df['input'] = input_type
            elif 'input' not in df.columns:
                # Если input не задан и не существует в данных, добавляем его со значением по умолчанию
                df['input'] = f"search:{search_query_term}"
            
            final_column_names_map = {}
            temp_new_columns = []
            original_input_col_name = None
            for col in df.columns:
                original_col_str = str(col)
                if original_col_str == 'id': new_name = 'video_id' 
                elif original_col_str == 'search_query': new_name = 'search_query'
                elif original_col_str == 'input': 
                    original_input_col_name = 'input' 
                    new_name = self.to_snake_case(original_col_str)
                else: new_name = self.to_snake_case(original_col_str)
                temp_new_columns.append(new_name)
                final_column_names_map[original_col_str] = new_name
            df.columns = temp_new_columns

            video_id_col_pg = final_column_names_map.get('id', 'video_id')
            input_col_pg = final_column_names_map.get(original_input_col_name or 'input', 'input')
            search_query_col_pg = final_column_names_map.get('search_query', 'search_query')
            
            # Проверяем, что у нас есть все необходимые столбцы
            print(f"Проверка столбцов после преобразования: video_id={video_id_col_pg}, input={input_col_pg}, search_query={search_query_col_pg}")
            print(f"Доступные столбцы: {list(df.columns)}")
            
            # Добавляем столбцы, если их нет
            if input_col_pg not in df.columns:
                df[input_col_pg] = f"search:{search_query_term}"
                print(f"Добавлен отсутствующий столбец: {input_col_pg}")
            
            if search_query_col_pg not in df.columns:
                df[search_query_col_pg] = search_query_term
                print(f"Добавлен отсутствующий столбец: {search_query_col_pg}")

            desired_order = [search_query_col_pg, input_col_pg, video_id_col_pg]
            core_cols_present = all(col in df.columns for col in desired_order)
            if not core_cols_present:
                print(f"Ошибка: Один из ключевых столбцов {desired_order} отсутствует в DataFrame после преобразования имен.")
                print(f"Доступные столбцы: {list(df.columns)}")
                return False
            
            remaining_cols = [col for col in df.columns if col not in desired_order]
            final_df_cols_order = desired_order + remaining_cols
            df = df[final_df_cols_order]
            
            print(f"Столбцы DataFrame для PostgreSQL (упорядоченные): {list(df.columns)}")
            if video_id_col_pg not in df.columns: return False # Дополнительная проверка, хотя выше должна была отработать
            print(f"Используемый столбец для логического PRIMARY KEY (YouTube ID): {video_id_col_pg}")
            
            # Выводим первую строку для проверки
            print(f"Пример данных (первая строка):")
            first_row = df.iloc[0] if not df.empty else None
            if first_row is not None:
                for col in desired_order:
                    print(f"  {col}: {first_row.get(col)}")
            
            conn = get_db_connection() # Используем новую функцию
            if not conn: # Проверяем, успешно ли установлено соединение
                print("Ошибка save_results_db: Не удалось установить соединение с базой данных.")
                return False
                
            cur = conn.cursor()

            # Создаем таблицу, если она не существует
            # Теперь используем SERIAL для автоинкрементального ID, video_id становится UNIQUE
            table_columns_definitions = ['"id" SERIAL PRIMARY KEY']
            for col_name in df.columns: 
                if col_name == video_id_col_pg:
                    # Делаем video_id уникальным, но не первичным ключом
                    table_columns_definitions.append(f'"{col_name}" TEXT UNIQUE')
                else:
                    table_columns_definitions.append(f'"{col_name}" TEXT')
            
            create_table_sql = f'''CREATE TABLE IF NOT EXISTS "{table_name}" (
                                    {', '.join(table_columns_definitions)}
                                );'''
            
            try:
                # Проверяем, существует ли таблица
                check_table_sql = f"SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = '{table_name}')"
                cur.execute(check_table_sql)
                table_exists = cur.fetchone()[0]
                
                current_df_columns_set = set(df.columns)

                if not table_exists:
                    # Если таблица не существует, создаем новую
                    cur.execute(create_table_sql)
                    print(f"Создана новая таблица '{table_name}' с автоинкрементным ID и столбцами: {', '.join(df.columns)}")
                else:
                    print(f"Таблица '{table_name}' уже существует. Проверка и обновление структуры...")
                    # Получаем существующие столбцы из БД
                    cur.execute(f"SELECT column_name FROM information_schema.columns WHERE table_schema = 'public' AND table_name = '{table_name}'")
                    existing_db_columns = {row[0] for row in cur.fetchall()}
                    print(f"Существующие столбцы в БД: {existing_db_columns}")
                    print(f"Требуемые столбцы из DataFrame: {current_df_columns_set}")

                    # Определяем недостающие столбцы
                    missing_columns = current_df_columns_set - existing_db_columns
                    
                    if missing_columns:
                        print(f"Обнаружены недостающие столбцы: {missing_columns}. Добавление...")
                        for col_to_add in missing_columns:
                            col_definition = f'"{col_to_add}" TEXT'
                            if col_to_add == video_id_col_pg:
                                col_definition += ' UNIQUE'
                            
                            alter_table_sql = f'ALTER TABLE "{table_name}" ADD COLUMN IF NOT EXISTS {col_definition};'
                            print(f"Выполнение: {alter_table_sql}")
                            cur.execute(alter_table_sql)
                        print(f"Столбцы {missing_columns} добавлены в таблицу '{table_name}'.")
                    else:
                        print("Структура таблицы соответствует данным. Дополнительные столбцы не требуются.")

                    # Проверяем, нужно ли изменить структуру существующей таблицы (PK)
                    check_pk_sql = f"""
                    SELECT pg_attribute.attname
                    FROM pg_index, pg_class, pg_attribute, pg_namespace
                    WHERE pg_class.oid = '{table_name}'::regclass
                    AND indrelid = pg_class.oid
                    AND pg_attribute.attrelid = pg_class.oid
                    AND pg_attribute.attnum = ANY(pg_index.indkey)
                    AND indisprimary
                    AND pg_namespace.oid = pg_class.relnamespace;
                    """ # Завершаем f-строку здесь
                    try:
                        cur.execute(check_pk_sql)
                        pk_column = cur.fetchone()
                        
                        if pk_column and pk_column[0] != 'id':
                            print(f"Обнаружен первичный ключ '{pk_column[0]}', который не 'id'. Если требуется, структура PK будет изменена.")
                            temp_table_name = f"{table_name}_temp_pk_fix"
                            
                            dynamic_table_columns_definitions = ['"id" SERIAL PRIMARY KEY']
                            for col_name_df in df.columns:
                                col_def_str = f'"{col_name_df}" TEXT'
                                if col_name_df == video_id_col_pg:
                                    col_def_str += ' UNIQUE'
                                dynamic_table_columns_definitions.append(col_def_str)

                            cur.execute(f"CREATE TABLE {temp_table_name} ({', '.join(dynamic_table_columns_definitions)})")
                            print(f"Создана временная таблица {temp_table_name} для исправления PK.")

                            cur.execute(f"SELECT column_name FROM information_schema.columns WHERE table_schema = 'public' AND table_name = '{table_name}'")
                            existing_columns_for_copy = {row[0] for row in cur.fetchall()}
                            
                            common_columns_for_copy_snake_case = current_df_columns_set.intersection(existing_columns_for_copy)
                            
                            if common_columns_for_copy_snake_case:
                                common_cols_str_quoted = ', '.join([f'"{col}"' for col in common_columns_for_copy_snake_case])
                                print(f"Копирование данных для общих столбцов: {common_cols_str_quoted}")
                                cur.execute(f"INSERT INTO {temp_table_name} ({common_cols_str_quoted}) SELECT {common_cols_str_quoted} FROM {table_name}")
                            
                            cur.execute(f"DROP TABLE {table_name}")
                            cur.execute(f"ALTER TABLE {temp_table_name} RENAME TO {table_name}")
                            print(f"Структура таблицы '{table_name}' успешно изменена (исправлен PK).")
                        elif not pk_column:
                             print(f"В таблице '{table_name}' отсутствует первичный ключ. Попытка добавить PK 'id'...")
                             if 'id' not in existing_db_columns:
                                 cur.execute(f'ALTER TABLE "{table_name}" ADD COLUMN "id" SERIAL PRIMARY KEY;')
                                 print("Добавлен первичный ключ 'id' SERIAL PRIMARY KEY.")
                             elif 'id' in existing_db_columns:
                                 cur.execute(f"SELECT COUNT(*) FROM information_schema.table_constraints tc JOIN information_schema.key_column_usage kcu ON tc.constraint_name = kcu.constraint_name AND tc.table_schema = kcu.table_schema WHERE tc.constraint_type = 'PRIMARY KEY' AND tc.table_name = '{table_name}' AND kcu.column_name = 'id';")
                                 id_is_pk = cur.fetchone()[0] > 0
                                 if not id_is_pk:
                                     try:
                                         cur.execute(f'ALTER TABLE "{table_name}" ADD PRIMARY KEY ("id");')
                                         print("Существующий столбец 'id' сделан первичным ключом.")
                                     except Exception as pk_add_e:
                                         print(f"Не удалось сделать 'id' первичным ключом: {pk_add_e}. Возможно, требуется ручное вмешательство или пересоздание таблицы.")

                    except Exception as struct_e:
                        print(f"Ошибка при проверке/изменении структуры таблицы (PK): {struct_e}")
                        conn.rollback()
                
                conn.commit()
            except Exception as e: 
                print(f"Ошибка при создании/модификации таблицы '{table_name}': {e}")
                conn.rollback()
                cur.close()
                conn.close()
                return False

            # Подготовка для операций вставки/обновления
            insert_columns_from_df = list(df.columns)
            db_column_names_str = ', '.join([f'"{col}"' for col in insert_columns_from_df])
            
            # Подготовка для построчной вставки данных
            new_records = 0
            updated_records = 0
            errors = 0

            for _, row_series in df.iterrows():
                try:
                    row_values = []
                    for col_name in insert_columns_from_df:
                        value = row_series.get(col_name)
                        if isinstance(value, (list, dict)):
                            processed_value = json.dumps(value, ensure_ascii=False)
                        elif pd.isnull(value):
                            processed_value = None
                        else:
                            processed_value = str(value)
                        row_values.append(processed_value)

                    # Генерация вопросительных знаков для параметров
                    placeholders = ', '.join(['%s'] * len(insert_columns_from_df))
                    video_id_val = row_series.get(video_id_col_pg)

                    # Проверяем существование записи
                    check_sql = f'SELECT 1 FROM "{table_name}" WHERE "{video_id_col_pg}" = %s'
                    cur.execute(check_sql, (video_id_val,))
                    exists = cur.fetchone() is not None

                    if exists:
                        # Обновление существующей записи
                        update_parts = [f'"{col}" = %s' for col in insert_columns_from_df]
                        update_sql = f'UPDATE "{table_name}" SET {", ".join(update_parts)} WHERE "{video_id_col_pg}" = %s'
                        # Добавляем id видео в конец списка для условия WHERE
                        update_values = row_values + [video_id_val]
                        cur.execute(update_sql, update_values)
                        updated_records += 1
                    else:
                        # Вставка новой записи - id автоматически генерируется SERIAL
                        insert_sql = f'INSERT INTO "{table_name}" ({db_column_names_str}) VALUES ({placeholders})'
                        cur.execute(insert_sql, row_values)
                        new_records += 1
                    
                    conn.commit()
                except Exception as e:
                    print(f"Ошибка при обработке строки: {e}")
                    errors += 1
                    conn.rollback()
            
            cur.close()
            conn.close()
            
            print(f"Сохранение в PostgreSQL завершено. Новых: {new_records}, Обновлено: {updated_records}, Ошибок: {errors}")
            return errors == 0
        except Exception as e: print(f"Критическая ошибка сохранения в PostgreSQL: {str(e)}"); import traceback; traceback.print_exc(); return False

    def to_snake_case(self, name):
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        s2 = re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()
        s3 = re.sub(r'[\s.-]+', '_', s2)
        s4 = re.sub(r'_+', '_', s3).strip('_')
        if not s4:
            return name.lower().replace('[^a-z0-9_]+', '') or 'unknown_column'
        return s4

# Блок if __name__ == "__main__" будет удален, 
# так как examples/youtube_search.py является основной точкой входа.
