"""
Модуль для взаимодействия с YouTube Downloader через Telegram-бота.

Этот модуль предоставляет функциональность для:
- Поиска видео по ключевым словам
- Загрузки аудио из видео
- Транскрибации аудио
- Получения списков видео из плейлистов и каналов

Автор: AI Team
"""

from .bot import (
    start, help_command, search_command, handle_message, button_callback,
    Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters,
    TELEGRAM_BOT_TOKEN
)

__all__ = [
    "start", "help_command", "search_command", "handle_message", "button_callback",
    "Application", "CommandHandler", "MessageHandler", "CallbackQueryHandler", "filters",
    "TELEGRAM_BOT_TOKEN"
]