import os
import google.generativeai as genai
from rich.console import Console # Может понадобиться для вывода статуса, но лучше минимизировать в провайдере

from src.transcriber.base_provider import ITranscriptionProvider, TranscriptionResult
from typing import Optional

class GeminiProvider(ITranscriptionProvider):
    PROVIDER_NAME = "gemini"

    def __init__(self, api_key: str, console: Optional[Console] = None):
        if not api_key:
            raise ValueError("API ключ для GeminiProvider не предоставлен.")
        self.api_key = api_key
        self.console = console if console else Console(quiet=True) # Тихий по умолчанию
        try:
            genai.configure(api_key=self.api_key)
        except Exception as e:
            # Логируем или обрабатываем ошибку конфигурации, если это необходимо
            # self.console.print(f"[bold red]Ошибка конфигурации Gemini API: {e}[/bold red]")
            raise ValueError(f"Ошибка конфигурации Gemini API: {e}")

    def get_provider_name(self) -> str:
        return self.PROVIDER_NAME

    def transcribe(
        self, 
        audio_file_path: str, 
        language: Optional[str] = None, # Пример: 'ru', 'en'
        prompt_text: Optional[str] = None,
        with_timestamps: bool = False,
        # model_name: str = 'gemini-1.5-flash', # Можно сделать настраиваемым
        **kwargs 
    ) -> TranscriptionResult:
        
        self.console.print(f"[GeminiProvider] Обработка файла: {audio_file_path}")

        if not os.path.exists(audio_file_path):
            return TranscriptionResult(error_message=f"Файл не найден: {audio_file_path}", success=False)

        # Определение MIME-типа на основе расширения (упрощенно, т.к. сервис выше должен обеспечить MP3/WAV)
        # В идеале, этот провайдер должен получать только форматы, которые он напрямую поддерживает,
        # или иметь внутреннюю логику для работы с MIME.
        # Для Gemini важен MIME, но если мы гарантируем MP3 на входе, то можно упростить.
        file_extension = os.path.splitext(audio_file_path)[1].lower()
        mime_types = {
            '.mp3': 'audio/mp3',
            '.wav': 'audio/wav',
            '.aiff': 'audio/aiff',
            '.aac': 'audio/aac',
            '.ogg': 'audio/ogg',
            '.flac': 'audio/flac'
            # .webm уже должен быть сконвертирован сервисом выше
        }
        mime_type = mime_types.get(file_extension, 'audio/mpeg') # По умолчанию MP3
        
        model_name = kwargs.get('model_name', 'gemini-1.5-flash') # Используем 1.5 Flash по умолчанию

        try:
            model = genai.GenerativeModel(model_name)
            
            # Формирование промпта для Gemini
            final_prompt = prompt_text if prompt_text else "Создай полную транскрипцию этого аудио."
            if with_timestamps and "таймкод" not in final_prompt.lower() and "timestamp" not in final_prompt.lower():
                final_prompt += " Добавь таймкоды формата [MM:SS] каждые 30 секунд."
            
            if language:
                language_names = {
                    'ru': 'русский', 'en': 'английский', 'fr': 'французский', 'de': 'немецкий',
                    'es': 'испанский', 'it': 'итальянский', 'pt': 'португальский',
                    'zh': 'китайский', 'ja': 'японский'
                }
                language_name = language_names.get(language.lower(), language)
                final_prompt += f"\nРезультат транскрибации должен быть на {language_name} языке."

            self.console.print(f"[GeminiProvider] Используемый промпт: {final_prompt[:100]}...")
            
            # Загрузка файла или передача байтов (логика из gemini_transcriber.py)
            # Для простоты провайдера, будем ожидать, что файл не слишком большой для inline
            # или что genai.upload_file используется до вызова этого метода, если файл большой.
            # В данном контексте сервиса, лучше передавать файл как объект.
            # Gemini API теперь предпочитает File API для аудио.
            self.console.print(f"[GeminiProvider] Загрузка файла {audio_file_path} для Gemini...")
            uploaded_file = genai.upload_file(path=audio_file_path, mime_type=mime_type)
            self.console.print(f"[GeminiProvider] Файл '{uploaded_file.display_name}' загружен. URI: {uploaded_file.uri}")

            # Подсчет входных токенов
            input_token_count_str = "Не удалось подсчитать"
            try:
                count_response = model.count_tokens([final_prompt, uploaded_file])
                input_token_count_str = str(count_response.total_tokens)
                self.console.print(f"[GeminiProvider] Примерное количество входных токенов: {input_token_count_str}")
            except Exception as count_e:
                self.console.print(f"[GeminiProvider] Ошибка при подсчете входных токенов: {count_e}")

            self.console.print(f"[GeminiProvider] Запрос транскрибации в Gemini... Модель: {model_name}")
            response = model.generate_content([final_prompt, uploaded_file])
            
            transcript_text = response.text
            self.console.print(f"[GeminiProvider] Транскрибация завершена. Длина текста: {len(transcript_text)}")

            # Информация о токенах
            output_token_count_str = "Недоступно"
            total_token_count_str = "Недоступно"
            try:
                output_token_count_str = str(response.usage_metadata.candidates_token_count)
                total_token_count_str = str(response.usage_metadata.total_token_count)
            except AttributeError:
                self.console.print("[GeminiProvider] Информация об использовании токенов недоступна в ответе API.")
            
            token_info = (
                f"Входные токены (оценка): {input_token_count_str}\n"
                f"Выходные токены: {output_token_count_str}\n"
                f"Общие токены (API): {total_token_count_str}"
            )

            # Важно: после использования файла через File API, его нужно удалить из Gemini, если он больше не нужен.
            # Пока не будем удалять автоматически, т.к. файл может быть переиспользован.
            # genai.delete_file(uploaded_file.name) 
            # self.console.print(f"[GeminiProvider] Загруженный файл {uploaded_file.name} удален из Gemini.")

            return TranscriptionResult(text=transcript_text, token_info=token_info, success=True)

        except Exception as e:
            self.console.print(f"[GeminiProvider] Ошибка при транскрибации: {e}")
            # import traceback
            # self.console.print(traceback.format_exc())
            return TranscriptionResult(error_message=str(e), success=False) 