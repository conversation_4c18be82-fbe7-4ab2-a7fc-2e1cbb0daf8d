# youtube_api_client.py
import os
import re
from datetime import datetime
from dotenv import load_dotenv
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Импортируем базовые функции из youtube_api.py
try:
    from src.scrapers.youtube_api.youtube_api import (
        get_youtube_service, search_videos, get_channel_details, 
        get_channel_videos, get_video_details, get_video_comments,
        get_video_categories, parse_duration, format_duration
    )
except ImportError:
    # Если не получается импортировать из абсолютного пути, пробуем относительный
    from .youtube_api import (
        get_youtube_service, search_videos, get_channel_details, 
        get_channel_videos, get_video_details, get_video_comments,
        get_video_categories, parse_duration, format_duration
    )

# Загружаем переменные окружения для доступа к API ключу
load_dotenv()
YOUTUBE_API_KEY = os.getenv("YOUTUBE_API_KEY")

def get_video_captions(video_id):
    """
    Получает список доступных субтитров для видео.
    
    Args:
        video_id (str): ID видео на YouTube.
        
    Returns:
        list: Список словарей с информацией о доступных субтитрах.
        
    Raises:
        ValueError: Если YOUTUBE_API_KEY не установлен.
        HttpError: При ошибках запросов к API.
    """
    try:
        youtube = get_youtube_service()
        
        # Запрос списка субтитров
        captions_response = youtube.captions().list(
            part='snippet',
            videoId=video_id
        ).execute()
        
        captions = []
        
        for item in captions_response.get('items', []):
            caption_info = {
                'id': item['id'],
                'language': item['snippet']['language'],
                'name': item['snippet']['name'],
                'track_kind': item['snippet']['trackKind'],
                'is_auto_synced': item['snippet']['isAutoSynced'],
                'is_draft': item['snippet']['isDraft'],
                'is_cc': item['snippet']['isCC'],
                'status': item['snippet']['status']
            }
            
            captions.append(caption_info)
            
        return captions
        
    except HttpError as e:
        print(f"Ошибка при получении субтитров видео: {e}")
        # Если субтитры недоступны, возвращаем пустой список
        if "has no captions" in str(e):
            return []
        raise
    except Exception as e:
        print(f"Непредвиденная ошибка при получении субтитров видео: {e}")
        raise

class YouTubeApiClient:
    """
    Класс для работы с YouTube Data API v3.
    
    Предоставляет единый интерфейс для доступа к функциям API и обработки ошибок.
    """
    
    def __init__(self, api_key=None):
        """
        Инициализирует клиент API.
        
        Args:
            api_key (str, optional): Ключ API YouTube. Если не указан, берется из переменных окружения.
        """
        self.api_key = api_key or YOUTUBE_API_KEY
        if not self.api_key:
            raise ValueError("API ключ YouTube не установлен. Обработка видео невозможна.")
        
        self.service = build('youtube', 'v3', developerKey=self.api_key)
    
    def search_videos(self, query, max_results=10, region_code="RU", relevance_language="ru", 
                     order="relevance", video_type=None, video_category=None):
        """
        Выполняет поиск видео на YouTube по заданному запросу.
        """
        return search_videos(query, max_results, region_code, relevance_language, order, video_type, video_category)
    
    def get_video_details(self, video_id):
        """
        Получает подробную информацию о видео по его ID.
        """
        return get_video_details(video_id)
    
    def get_channel_details(self, channel_id):
        """
        Получает информацию о канале YouTube по его ID.
        """
        return get_channel_details(channel_id)
    
    def get_channel_videos(self, channel_id, max_results=50):
        """
        Получает список видео канала YouTube.
        """
        return get_channel_videos(channel_id, max_results)
    
    def get_video_comments(self, video_id, max_results=100, order='time', text_format='plainText'):
        """
        Получает комментарии к видео.
        """
        return get_video_comments(video_id, max_results, order, text_format)
    
    def get_video_categories(self, region_code="RU"):
        """
        Получает список категорий видео.
        """
        return get_video_categories(region_code)
    
    def get_video_captions(self, video_id):
        """
        Получает список доступных субтитров для видео.
        """
        return get_video_captions(video_id)
    
    def get_related_videos(self, video_id, max_results=10):
        """
        Получает список связанных видео.
        
        Args:
            video_id (str): ID видео на YouTube.
            max_results (int, optional): Максимальное количество результатов. По умолчанию 10.
            
        Returns:
            list: Список словарей с информацией о связанных видео.
        """
        try:
            # Используем поиск для нахождения связанных видео
            search_response = self.service.search().list(
                part='snippet',
                relatedToVideoId=video_id,
                type='video',
                maxResults=max_results
            ).execute()
            
            # Извлечение ID видео для получения дополнительной информации
            video_ids = [item['id']['videoId'] for item in search_response['items']]
            
            # Получение детальной информации
            if video_ids:
                videos_response = self.service.videos().list(
                    part='snippet,contentDetails,statistics',
                    id=','.join(video_ids)
                ).execute()
                
                # Создание словаря для быстрого доступа
                videos_data = {video['id']: video for video in videos_response['items']}
                
                # Формирование результата
                results = []
                for item in search_response['items']:
                    video_id = item['id']['videoId']
                    if video_id in videos_data:
                        video_info = {
                            'id': video_id,
                            'title': item['snippet']['title'],
                            'description': item['snippet']['description'],
                            'thumbnails': item['snippet']['thumbnails'],
                            'published_at': item['snippet']['publishedAt'],
                            'channel_id': item['snippet']['channelId'],
                            'channel_title': item['snippet']['channelTitle'],
                            'url': f'https://www.youtube.com/watch?v={video_id}'
                        }
                        
                        # Добавление данных из videos_data
                        if 'contentDetails' in videos_data[video_id]:
                            video_info['duration'] = videos_data[video_id]['contentDetails']['duration']
                            video_info['duration_seconds'] = parse_duration(video_info['duration'])
                            video_info['duration_formatted'] = format_duration(video_info['duration'])
                        
                        if 'statistics' in videos_data[video_id]:
                            video_info['statistics'] = videos_data[video_id]['statistics']
                            video_info['view_count'] = int(videos_data[video_id]['statistics'].get('viewCount', 0))
                            video_info['like_count'] = int(videos_data[video_id]['statistics'].get('likeCount', 0))
                            video_info['comment_count'] = int(videos_data[video_id]['statistics'].get('commentCount', 0))
                            
                        results.append(video_info)
                
                return results
            
            return []
            
        except HttpError as e:
            print(f"Ошибка при получении связанных видео: {e}")
            raise
        except Exception as e:
            print(f"Непредвиденная ошибка при получении связанных видео: {e}")
            raise
    
    def get_complete_video_data(self, video_id):
        """
        Получает полный набор данных о видео, включая детали, комментарии и связанные видео.
        
        Args:
            video_id (str): ID видео на YouTube.
            
        Returns:
            dict: Словарь с полной информацией о видео.
        """
        try:
            # Получаем базовую информацию о видео
            video_details = self.get_video_details(video_id)
            
            if not video_details:
                return None
                
            # Добавляем информацию о канале
            channel_details = self.get_channel_details(video_details['channel_id'])
            if channel_details:
                video_details['channel_details'] = channel_details
                
            # Пытаемся получить комментарии
            try:
                comments = self.get_video_comments(video_id, max_results=50)
                video_details['comments'] = comments
            except:
                video_details['comments'] = []
                
            # Пытаемся получить субтитры
            try:
                captions = self.get_video_captions(video_id)
                video_details['captions'] = captions
            except:
                video_details['captions'] = []
                
            # Получаем связанные видео
            try:
                related_videos = self.get_related_videos(video_id, max_results=10)
                video_details['related_videos'] = related_videos
            except:
                video_details['related_videos'] = []
                
            # Дополнительные поля для удобства
            video_details['upload_date'] = video_details['published_at'][:10]  # YYYY-MM-DD
            
            # Преобразуем дату в формат YYYYMMDD для совместимости с yt-dlp
            upload_date_obj = datetime.strptime(video_details['upload_date'], '%Y-%m-%d')
            video_details['upload_date_str'] = upload_date_obj.strftime('%Y%m%d')
            
            return video_details
            
        except Exception as e:
            print(f"Ошибка при получении полной информации о видео: {e}")
            return None
            
    def get_metadata_for_download(self, video_id):
        """
        Получает метаданные видео в формате, подходящем для VideoDownloader
        
        Args:
            video_id (str): ID видео на YouTube.
            
        Returns:
            dict: Словарь с метаданными для VideoDownloader.
        """
        try:
            video_details = self.get_video_details(video_id)
            
            if not video_details:
                return None
                
            # Преобразуем в формат, подходящий для use_in_main.py
            metadata = {
                'id': video_details['id'],
                'title': video_details['title'],
                'webpage_url': video_details['url'],
                'upload_date': video_details.get('upload_date_str') or datetime.strptime(video_details['published_at'][:10], '%Y-%m-%d').strftime('%Y%m%d'),
                'duration': video_details['duration_seconds'],
                'channel': video_details['channel_title'],
                'channel_id': video_details['channel_id'],
                'channel_url': f'https://www.youtube.com/channel/{video_details["channel_id"]}',
                'view_count': video_details['view_count'],
                'like_count': video_details['like_count'],
                'comment_count': video_details['comment_count'],
                'tags': video_details.get('tags', []),
                'thumbnails': video_details['thumbnails'],
                # Добавление других полезных полей
                'description': video_details['description'],
                'privacy_status': video_details['privacy_status'],
                'license': video_details.get('license'),
                'has_captions': video_details.get('caption', False)
            }
            
            return metadata
            
        except Exception as e:
            print(f"Ошибка при получении метаданных для загрузки: {e}")
            return None


if __name__ == "__main__":
    # Пример использования
    if YOUTUBE_API_KEY:
        try:
            print("Тестирование YouTubeApiClient...")
            
            client = YouTubeApiClient()
            
            print("\n1. Поиск видео:")
            search_query = input("Введите поисковый запрос: ")
            results = client.search_videos(search_query, max_results=5)
            
            for i, video in enumerate(results, 1):
                print(f"\n{i}. {video['title']}")
                print(f"   URL: {video['url']}")
                print(f"   Канал: {video['channel_title']}")
                print(f"   Опубликовано: {video['published_at']}")
            
            if results:
                selected_index = int(input("\nВыберите видео (номер) для подробной информации: ")) - 1
                if 0 <= selected_index < len(results):
                    test_video_id = results[selected_index]['id']
                    
                    print("\n2. Получение полной информации о видео:")
                    video_data = client.get_complete_video_data(test_video_id)
                    
                    if video_data:
                        print(f"\nПодробная информация о видео '{video_data['title']}':")
                        print(f"URL: {video_data['url']}")
                        print(f"Канал: {video_data['channel_title']}")
                        print(f"Продолжительность: {video_data['duration_formatted']} ({video_data['duration_seconds']} сек)")
                        print(f"Просмотров: {video_data['view_count']}")
                        print(f"Лайков: {video_data['like_count']}")
                        print(f"Комментариев: {video_data['comment_count']}")
                        print(f"Статус приватности: {video_data['privacy_status']}")
                        print(f"Описание: {video_data['description'][:150]}..." if len(video_data['description']) > 150 else f"Описание: {video_data['description']}")
                        
                        print("\nДоступные субтитры:")
                        if video_data['captions']:
                            for i, caption in enumerate(video_data['captions'], 1):
                                print(f"   {i}. {caption['language']} - {caption['name']}")
                        else:
                            print("   Субтитры не найдены")
                        
                        print(f"\nКомментарии ({len(video_data['comments'])} загружено):")
                        for i, comment in enumerate(video_data['comments'][:3], 1):
                            print(f"\n{i}. {comment['author_display_name']} ({comment['published_at'][:10]}):")
                            print(f"   {comment['text'][:100]}..." if len(comment['text']) > 100 else f"   {comment['text']}")
                        
                        print(f"\nСвязанные видео ({len(video_data['related_videos'])} загружено):")
                        for i, related in enumerate(video_data['related_videos'][:3], 1):
                            print(f"   {i}. {related['title']}")
                            
                        # Демонстрация метаданных для загрузки
                        print("\n3. Метаданные для загрузки через VideoDownloader:")
                        metadata = client.get_metadata_for_download(test_video_id)
                        for key, value in metadata.items():
                            if key not in ['thumbnails', 'tags', 'description']:
                                print(f"   {key}: {value}")
        
        except ValueError as ve:
            print(f"Ошибка конфигурации: {ve}")
        except HttpError as he:
            print(f"Ошибка API: {he}")
        except Exception as e:
            print(f"Произошла ошибка: {e}")
    else:
        print("\nТестирование не может быть выполнено, так как YOUTUBE_API_KEY не установлен.")
        print("Добавьте YOUTUBE_API_KEY в файл .env")