"""
Пример использования YouTube Scraper API

Этот скрипт демонстрирует работу с библиотекой YouTubeScraper.
"""

import sys
import os

# Добавляем корневую директорию проекта в путь для импорта
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.scrapers.apify import YouTubeScraper
from src.db_utils import test_postgres_connection # Импорт тестовой функции
import argparse
import sys

def main():
    # В самом начале вызываем тест соединения с БД
    print("="*30 + " ЗАПУСК ТЕСТА СОЕДИНЕНИЯ С БД " + "="*30)
    if not test_postgres_connection():
        print("Тест соединения с БД НЕ ПРОЙДЕН. Проверьте настройки и доступность БД.")
        # Решите, хотите ли вы прерывать выполнение, если тест не пройден
        # sys.exit(1) 
    else:
        print("Тест соединения с БД пройден успешно.")
    print("="*30 + " ЗАВЕРШЕНИЕ ТЕСТА СОЕДИНЕНИЯ С БД " + "="*30 + "\n")

    parser = argparse.ArgumentParser(description='YouTube Scraper с использованием различных API')
    
    # Основные аргументы
    parser.add_argument('--mode', type=str, choices=['search', 'channel', 'playlist'], 
                      default='search', help='Режим работы: поиск, канал или плейлист')
    parser.add_argument('--query', type=str, help='Поисковый запрос или URL канала/плейлиста')
    parser.add_argument('--max', type=int, default=5, help='Максимальное количество результатов')
    parser.add_argument('--output', type=str, default='youtube_results.json', 
                      help='Имя файла для сохранения результатов')
    parser.add_argument('--scraper', type=str, default='apify', 
                      choices=['apify', 'youtube_api', 'zenno'], 
                      help='Тип скрапера для использования')
    
    args = parser.parse_args()
    
    # Проверка аргументов
    if not args.query:
        print("Ошибка: не указан поисковый запрос или URL")
        parser.print_help()
        sys.exit(1)
    
    # Выбор скрапера в зависимости от аргумента
    if args.scraper == 'apify':
        scraper = YouTubeScraper()
    elif args.scraper == 'youtube_api':
        print("Скрапер YouTube API пока не реализован")
        sys.exit(1)
    elif args.scraper == 'zenno':
        print("Скрапер ZennoPoSter пока не реализован")
        sys.exit(1)
    else:
        print(f"Неизвестный тип скрапера: {args.scraper}")
        sys.exit(1)
    
    print(f"Режим: {args.mode}")
    print(f"Запрос: {args.query}")
    print(f"Макс. результатов: {args.max}")
    print(f"Выходной файл: {args.output}")
    print(f"Тип скрапера: {args.scraper}")
    print("-" * 50)
    
    # Выполнение запроса в зависимости от режима
    if args.mode == 'search':
        results = scraper.search_by_query(args.query, max_results=args.max)
    elif args.mode == 'channel':
        results = scraper.get_channel_videos(args.query, max_results=args.max)
    elif args.mode == 'playlist':
        results = scraper.get_playlist_videos(args.query, max_results=args.max)
    
    # Сохранение результатов
    if results:
        # Сохранение в JSON
        scraper.save_results(results, args.output)
        
        # Сохранение в таблицу CSV
        csv_filename = os.path.splitext(args.output)[0] + '.csv'
        scraper.save_results_table(results, csv_filename)
        
        # Сохранение в базу данных с указанием типа скрапинга
        search_query_term = args.query
        input_type = f"{args.mode}:{args.query}"  # Например: "search:python tutorial" или "channel:url"
        db_result = scraper.save_results_db(
            results, 
            search_query_term=search_query_term, 
            table_name='youtube_videos_pg',
            input_type=input_type
        )
        if db_result:
            print(f"Результаты успешно сохранены в базу данных.")
        else:
            print(f"Не удалось сохранить результаты в базу данных.")
        
        # Вывод информации о первых результатах
        print("\nПолученные результаты:")
        for i, video in enumerate(results[:min(3, len(results))]):
            print(f"\nРезультат #{i+1}:")
            scraper.print_video_info(video)
        
        print(f"\nВсего получено результатов: {len(results)}")
        print(f"Результаты сохранены в файлы: {args.output} и {csv_filename}")
    else:
        print("Результаты не получены или произошла ошибка.")

if __name__ == "__main__":
    main()
