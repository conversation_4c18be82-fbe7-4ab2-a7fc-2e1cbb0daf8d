"""
Модуль для отправки видео на транскрибацию.
"""

import os
import subprocess
import imageio_ffmpeg
from rich.console import Console
from typing import Optional, Dict, Any

from src.transcriber.base_provider import TranscriptionResult
from src.transcriber.providers.gemini_provider import GeminiProvider
# Когда появится OpenAI провайдер, его нужно будет импортировать сюда же
# from .providers.openai_provider import OpenAIProvider

class TranscriptionService:
    """
    Сервис для выполнения транскрибации аудиофайлов с использованием различных провайдеров.
    """
    def __init__(self, console: Optional[Console] = None, api_key: Optional[str] = None):
        self.console = console if console else Console(quiet=True)
        self.api_key = api_key
        self._ffmpeg_exe = None

    def _get_ffmpeg_exe(self) -> Optional[str]:
        if self._ffmpeg_exe is None:
            try:
                self._ffmpeg_exe = imageio_ffmpeg.get_ffmpeg_exe()
                self.console.print(f"[TranscriptionService] ffmpeg найден: {self._ffmpeg_exe}", style="dim")
            except FileNotFoundError:
                self.console.print("[TranscriptionService] [bold red]Ошибка: ffmpeg не найден. imageio-ffmpeg должен быть установлен.[/bold red]")
                self._ffmpeg_exe = "not_found"
        return None if self._ffmpeg_exe == "not_found" else self._ffmpeg_exe

    def _convert_to_mp3_if_needed(self, audio_file_path: str) -> Optional[str]:
        """
        Конвертирует WebM файл в MP3. Возвращает путь к MP3 или None в случае ошибки.
        MP3 файл сохраняется в той же директории, что и исходный WebM.
        """
        if not audio_file_path.lower().endswith('.webm'):
            return audio_file_path # Конвертация не требуется

        self.console.print(f"[TranscriptionService] Обнаружен файл WebM: {audio_file_path}", style="blue")
        mp3_file_path = os.path.splitext(audio_file_path)[0] + ".mp3"
        self.console.print(f"[TranscriptionService] Попытка конвертации в MP3: {mp3_file_path}", style="blue")

        ffmpeg_exe = self._get_ffmpeg_exe()
        if not ffmpeg_exe:
            self.console.print("[TranscriptionService] [bold red]Конвертация невозможна: ffmpeg не доступен.[/bold red]")
            return None

        try:
            cmd = [
                ffmpeg_exe,
                '-i', audio_file_path,
                '-vn', '-ab', '192k', '-ar', '44100', '-y',
                mp3_file_path
            ]
            self.console.print(f"[TranscriptionService] Выполнение команды: {' '.join(cmd)}", style="dim")
            process = subprocess.run(cmd, capture_output=True, text=True, check=False, encoding='utf-8', errors='ignore')
            
            if process.returncode == 0:
                self.console.print(f"[TranscriptionService] [bold green]Конвертация в MP3 успешно завершена:[/bold green] {mp3_file_path}")
                return mp3_file_path
            else:
                self.console.print(f"[TranscriptionService] [bold red]Ошибка конвертации WebM в MP3.[/bold red]")
                self.console.print(f"FFmpeg stdout: {process.stdout}", style="dim")
                self.console.print(f"FFmpeg stderr: {process.stderr}", style="dim")
                return None
        except Exception as e:
            self.console.print(f"[TranscriptionService] [bold red]Неожиданная ошибка при конвертации WebM в MP3:[/bold red] {e}")
            return None

    def transcribe(
        self,
        audio_file_path: str,
        provider_name: str,
        api_key: Optional[str] = None,
        provider_options: Optional[Dict[str, Any]] = None
    ) -> TranscriptionResult:
        """
        Выполняет транскрибацию, используя указанного провайдера.

        Args:
            audio_file_path: Путь к исходному аудиофайлу (может быть .webm).
            provider_name: Имя провайдера (например, 'gemini').
            api_key: API ключ для провайдера. Если None, будет попытка получить из переменных окружения (зависит от провайдера).
            provider_options: Словарь с дополнительными опциями для провайдера
                              (например, {'language': 'en', 'with_timestamps': True, 'prompt_text': '...'}).

        Returns:
            Объект TranscriptionResult.
        """
        if not provider_name:
            return TranscriptionResult(error_message="Имя провайдера транскрибации должно быть указано.", success=False)
        
        if not os.path.exists(audio_file_path):
            return TranscriptionResult(error_message=f"Исходный аудиофайл не найден: {audio_file_path}", success=False)

        # Шаг 1: Конвертация, если это WebM
        processed_audio_path = self._convert_to_mp3_if_needed(audio_file_path)
        if not processed_audio_path:
            return TranscriptionResult(error_message=f"Ошибка подготовки аудиофайла (возможно, ошибка конвертации): {audio_file_path}", success=False)
        
        if not os.path.exists(processed_audio_path):
             return TranscriptionResult(error_message=f"Обработанный аудиофайл не найден после конвертации: {processed_audio_path}", success=False)

        # Шаг 2: Выбор и вызов провайдера
        provider_options = provider_options or {}
        current_provider = None

        if provider_name.lower() == GeminiProvider.PROVIDER_NAME:
            # Получение API ключа для Gemini
            # Приоритет: аргумент метода -> self.api_key (из конструктора) -> переменная окружения
            gemini_api_key = api_key  # Из аргументов метода transcribe
            if not gemini_api_key:
                gemini_api_key = self.api_key # Из конструктора TranscriptionService
            if not gemini_api_key:
                gemini_api_key = os.getenv("GEMINI_API_KEY") # Из переменной окружения
            
            if not gemini_api_key:
                return TranscriptionResult(error_message="API ключ для Gemini не найден (ни в параметрах, ни в GEMINI_API_KEY).", success=False)
            
            try:
                current_provider = GeminiProvider(api_key=gemini_api_key, console=self.console)
            except ValueError as ve: # Ошибка инициализации провайдера (например, конфигурации API)
                 return TranscriptionResult(error_message=f"Ошибка инициализации Gemini провайдера: {ve}", success=False)
        # elif provider_name.lower() == OpenAIProvider.PROVIDER_NAME: # Для будущего OpenAI
        #     openai_api_key = api_key
        #     if not openai_api_key:
        #         openai_api_key = os.getenv("OPENAI_API_KEY")
        #     if not openai_api_key:
        #         return TranscriptionResult(error_message="API ключ для OpenAI не найден.", success=False)
        #     current_provider = OpenAIProvider(api_key=openai_api_key, console=self.console)
        else:
            return TranscriptionResult(error_message=f"Неподдерживаемый провайдер транскрибации: {provider_name}", success=False)

        # Вызов метода transcribe у выбранного провайдера
        try:
            self.console.print(f"[TranscriptionService] Вызов провайдера '{current_provider.get_provider_name()}' для файла: {processed_audio_path}")
            result = current_provider.transcribe(
                audio_file_path=processed_audio_path,
                language=provider_options.get('language'),
                prompt_text=provider_options.get('prompt_text'),
                with_timestamps=provider_options.get('with_timestamps', False),
                **provider_options.get('additional_kwargs', {}) # Для передачи любых других специфичных kwargs
            )
            return result
        except Exception as e:
            self.console.print(f"[TranscriptionService] [bold red]Ошибка во время транскрибации провайдером {provider_name}: {e}[/bold red]")
            # import traceback
            # self.console.print(traceback.format_exc(),style="dim")
            return TranscriptionResult(error_message=f"Ошибка выполнения у провайдера {provider_name}: {e}", success=False)